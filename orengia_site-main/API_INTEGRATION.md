# API Integration Guide

This document explains how the application is structured to easily integrate with a real API later.

## Current Implementation

The application currently uses a **simulated API layer** that mimics real API calls with:
- Realistic response delays (200-700ms)
- Proper error handling
- Loading states
- JSON data structure
- RESTful endpoint patterns

## API Service Layer

### File: `src/services/api.js`

This file contains the `ApiService` class that simulates API calls. When you're ready to integrate with a real API, you only need to modify this file.

#### Current Structure:
```javascript
// Simulated API call
async makeRequest(endpoint, options = {}) {
  await delay(Math.random() * 500 + 200); // Simulate network delay
  return this.getMockData(endpoint, options);
}
```

#### Real API Integration:
```javascript
// Real API call (replace the simulation)
async makeRequest(endpoint, options = {}) {
  const response = await fetch(`${API_CONFIG.baseURL}${endpoint}`, {
    method: options.method || 'GET',
    headers: API_CONFIG.headers,
    body: options.body ? JSON.stringify(options.body) : undefined,
    ...options
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}
```

## API Endpoints

### Products
- `GET /products` - Get all products with optional filters
- `GET /products/:id` - Get product by ID
- `GET /categories` - Get all categories

### Forms
- `POST /contact` - Submit contact form
- `POST /quote` - Submit quote request

## Data Structure

### Product Object
```json
{
  "id": 1,
  "name": "Product Name",
  "slug": "product-name",
  "category": "Category Name",
  "categorySlug": "category-slug",
  "description": "Short description",
  "longDescription": "Detailed description with multiple paragraphs",
  "brand": "Brand Name",
  "price": null,
  "inStock": true,
  "featured": true,
  "image": "/path/to/image.jpg",
  "gallery": ["/path/to/image1.jpg", "/path/to/image2.jpg"],
  "specifications": {
    "key": "value"
  },
  "tags": ["tag1", "tag2"],
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z"
}
```

### API Response Format
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "total": 100,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  }
}
```

## Custom Hooks

### File: `src/hooks/useApi.js`

The application uses custom React hooks for data fetching:

- `useProducts(params)` - Fetch products with filters
- `useProduct(id)` - Fetch single product
- `useFeaturedProducts(limit)` - Fetch featured products
- `useCategories()` - Fetch categories
- `useContactForm()` - Handle contact form submission

These hooks provide:
- Loading states
- Error handling
- Automatic refetching
- Caching (when using React Query - recommended for production)

## Environment Configuration

### File: `.env.example`

```env
VITE_API_URL=http://localhost:3001/api
VITE_ENV=development
```

Copy this to `.env` and update the API URL for your backend.

**Note**: Vite uses `VITE_` prefix for environment variables instead of `REACT_APP_`.

## Migration Steps

### 1. Set up your backend API
Ensure your API follows the expected endpoint structure and response format.

### 2. Update API configuration
```javascript
// In src/services/api.js
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'https://your-api.com/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    // Add authentication headers if needed
    // 'Authorization': `Bearer ${token}`
  }
};
```

### 3. Replace mock data with real API calls
In `src/services/api.js`, replace the `getMockData` method with actual fetch calls.

### 4. Update data structure (if needed)
If your API returns different data structures, update the JSON file and type definitions.

### 5. Add authentication (if needed)
```javascript
// Add token management
class ApiService {
  setAuthToken(token) {
    this.token = token;
    API_CONFIG.headers.Authorization = `Bearer ${token}`;
  }

  // Add to requests
  async makeRequest(endpoint, options = {}) {
    // Add auth logic here
  }
}
```

### 6. Error handling
Update error handling to match your API's error response format:

```javascript
if (!response.ok) {
  const errorData = await response.json();
  throw new Error(errorData.message || `API Error: ${response.status}`);
}
```

### 7. Add React Query (Recommended)
For production, consider adding React Query for better caching and synchronization:

```bash
npm install @tanstack/react-query
```

## Testing

### API Testing
1. Test all endpoints with tools like Postman
2. Verify response formats match expected structure
3. Test error scenarios (404, 500, network errors)
4. Test with different data sets

### Frontend Testing
1. Test loading states
2. Test error handling
3. Test form submissions
4. Test navigation and routing

## Performance Considerations

1. **Image Optimization**: Implement lazy loading and responsive images
2. **Caching**: Use React Query or SWR for intelligent caching
3. **Pagination**: Implement for large product lists
4. **Search**: Add debounced search functionality
5. **SEO**: Add meta tags and structured data

## Security

1. **Input Validation**: Validate all form inputs
2. **XSS Protection**: Sanitize user inputs
3. **HTTPS**: Use HTTPS in production
4. **Rate Limiting**: Implement on the backend
5. **Authentication**: Add proper auth if needed

## Deployment

1. **Environment Variables**: Set production API URLs
2. **Build Optimization**: Run `npm run build`
3. **CDN**: Use CDN for static assets
4. **Monitoring**: Add error tracking (Sentry, etc.)

This structure ensures a smooth transition from mock data to a real API with minimal code changes.
