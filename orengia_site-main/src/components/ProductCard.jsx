import { Link } from 'react-router-dom';

const ProductCard = ({ product }) => {
  return (
    <Link to={`/products/${product.id}`} className="no-underline">
      <div className="bg-[#FCFCFD] rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <div className='w-full h-44'>
        <div className='w-full h-full relative'>
          {/* designs */}
          <div className='absolute w-fit h-fit left-[50%] translate-x-[-50%] top-[-127%]'>
            <div className='w-[19rem] h-[19rem] scale-[120%] bg-[#FFE8D9] rounded-full'></div>
          </div>
          {/* img */}
          <div className='absolute w-fit h-fit left-[50%] translate-x-[-50%] top-[15%]'>
            <div className='w-[9rem] h-[9rem]'>
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-sm">No Image</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className='w-full h-40 overflow-hidden'>
        <div className='w-full h-full relative'>
          {/* designs */}
          <div className='absolute w-fit h-fit left-[50%] translate-x-[-50%] top-[37%]'>
            <div className='bg-[#F66408] w-10 h-[0.25rem] rounded-full'></div>
          </div>
          {/* details */}
          <div className='w-full h-full'>
            <div className='w-full h-full flex flex-col items-center gap-3'>
              <p className='text-[#040A22] text-xl font-medium max-w-[12rem] text-center'>{product.name}</p>
              <p className='text-[#747474] text-md font-light max-w-[12rem] text-center line-clamp-3'>
                {product.description}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    </Link>
  );
};

export default ProductCard;
