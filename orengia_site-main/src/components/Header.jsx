import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import searchIcon from "../assets/icons/search-icon.svg";
import logo from "../assets/logos/logo.svg";
import name from "../assets/logos/name.svg"

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Product', href: '/products' },
    { name: 'Brand', href: '/brand' },
    { name: 'Contact Us', href: '/contact' },
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <header style={
    !isMenuOpen
      ? {
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          WebkitMaskImage: 'linear-gradient(to bottom, black 20%, transparent 100%)',
          maskImage: 'linear-gradient(to bottom, black 20%, transparent 100%)',
          WebkitMaskSize: '100% 100%',
          maskSize: '100% 100%',
        }
      : undefined
  } className={`text-[#f0f0f0] ${isMenuOpen? 'backdrop-blur-2xl bg-black/60' : ''} pb-5 md:pb-0 pt-3 md:pt-0 fixed w-full top-0 z-50 flex flex-col justify-center items-center`}>
      <div className="max-w-7xl max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-22 my-3">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center">
              <div className="h-12 w-fit md:h-16 rounded-full flex items-center justify-between gap-5">                
                <img className='w-12 h-12 md:w-16 md:h-16' src={logo} alt="Logo" />
                <img className='w-30 h-12 md:w-35 md:h-16' src={name} alt="Logo" />
              </div>
            </Link>
          </div>

          <div className='desktop-nav-and-search-icon-wrapper flex flex-row items-center'>
            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8 flex-row transition-all duration-200">
              {navigation.map((item) => (
                <div className='w-fit h-fit hover:scale-105'>
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`px-3 py-2 w-fit h-fit text-md font-medium transition-colors duration-200 ${isActive(item.href)
                        ? 'text-orange-500'
                        : 'text-white hover:text-orange-500'
                      }`}
                  >
                    {item.name}
                  </Link>
                </div>
              ))}
            </nav>

            {/* Search Icon */}
            <div className=" md:flex items-center w-fit h-fit">
              <button className="p-2 w-fit h-fit text-white hover:text-orange-500 transition-colors duration-200">
                <div className='w-fit pl-10 h-fit flex flex-row justify-end'>
                  <img className='w-5 h-5' src={searchIcon} alt="Search" />
                </div>
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-white hover:text-orange-500 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden ">
            <div className="pb-3 space-y-1 bg-navy-800">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`block px-3 py-2 text-base font-medium transition-colors duration-200 ${isActive(item.href)
                      ? 'text-orange-500 bg-navy-700'
                      : 'text-white hover:text-orange-500 hover:bg-navy-700'
                    }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
