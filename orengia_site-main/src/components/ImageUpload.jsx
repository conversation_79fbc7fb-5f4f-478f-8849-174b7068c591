import { useState } from 'react';

const ImageUpload = ({ label, value, onChange, placeholder }) => {
  const [preview, setPreview] = useState(value || '');
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');

  const handleUrlChange = (e) => {
    const url = e.target.value;
    setPreview(url);
    onChange(url);
    setUploadError('');
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    setUploadError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const imageUrl = result.data.url;
        setPreview(imageUrl);
        onChange(imageUrl);
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadError(error.message || 'Failed to upload image');
      
      // Fallback: create preview with FileReader for user experience
      const reader = new FileReader();
      reader.onload = (event) => {
        setPreview(event.target.result);
      };
      reader.readAsDataURL(file);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-400">
        {label}
      </label>
      
      {/* URL Input */}
      <div>
        <input
          type="url"
          value={value || ''}
          onChange={handleUrlChange}
          placeholder={placeholder || "Enter image URL or upload file below"}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      {/* File Upload */}
      <div>
        <label className="block">
          <span className="sr-only">Choose file</span>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
            className="block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100 disabled:opacity-50 disabled:cursor-not-allowed"
          />
        </label>
        {uploading && (
          <p className="text-sm text-blue-600 mt-1">Uploading...</p>
        )}
        {uploadError && (
          <p className="text-sm text-red-600 mt-1">{uploadError}</p>
        )}
      </div>

      {/* Preview */}
      {preview && (
        <div className="mt-3">
          <img
            src={preview}
            alt="Preview"
            className="w-32 h-32 object-cover rounded-lg border border-gray-200"
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Helper Text */}
      <p className="text-xs text-gray-500">
        Upload images (max 5MB) or enter image URL. Supported formats: JPG, PNG, WEBP
      </p>
    </div>
  );
};

export default ImageUpload;
