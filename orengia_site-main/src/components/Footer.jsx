import { Link } from 'react-router-dom';
import logo from '../assets/logos/logo.svg';

const Footer = () => {
  return (
    <footer className="bg-navy-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Company Info */}
          <div className="col-span-1">
            <div className="flex items-center mb-4">
              <div className="w-12 lg:w-14 h-12 lg:h-14 rounded-full flex items-center justify-center mr-3">
                <img src={logo} alt="Orangeya Logo" className="w-full h-full" />
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Orangeya is a leading company that started more because 
              we want to be HONEST and serve the PURPOSE.
            </p>
          </div>

          {/* Products */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Products</h3>
            <ul className="space-y-2">
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Artemia Cysts</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Brine Shrimp Flakes</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Maturation Feeds</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Feeds</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Probiotics</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Water Conditioner</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Multivitamins</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-orange-500 transition-colors duration-200">Others</Link></li>
            </ul>
          </div>

          {/* Our Brands */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Our Brands</h3>
            <ul className="space-y-2">
              <li><span className="text-gray-300">Ocean Star International-USA</span></li>
              <li><span className="text-gray-300">Viet-Huab Taiwan</span></li>
              <li><span className="text-gray-300">Megasupply- USA</span></li>
            </ul>
          </div>

          {/* Contact Us */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <div className="space-y-3">
              <div className="text-gray-300">
                <p className="font-medium">East Coast Marine Products and Supplies</p>
              </div>
              <div className="text-gray-300">
                <p>No.294, Thambu Chetty Street, Parrys,</p>
                <p>Chennai - 600 001</p>
              </div>
              <div className="text-gray-300">
                <p>+91-97798-73877</p>
                <p>+91-99625-75791</p>
              </div>
              <div className="text-gray-300">
                <p>Email: <EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Border */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="text-center text-gray-400 text-sm">
            <p>&copy; {new Date().getFullYear()} East Coast Marine Products and Supplies. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
