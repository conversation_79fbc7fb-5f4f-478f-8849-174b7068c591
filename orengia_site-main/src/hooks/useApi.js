import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/api';

// Generic API hook
export const useApi = (apiCall, dependencies = []) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiCall();
      setData(response.data);
    } catch (err) {
      setError(err.message);
      setData(null);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
};

// Products hooks
export const useProducts = (params = {}) => {
  return useApi(() => apiService.getProducts(params), [JSON.stringify(params)]);
};

export const useProduct = (id) => {
  return useApi(() => apiService.getProductById(id), [id]);
};

export const useFeaturedProducts = (limit = 4) => {
  return useApi(() => apiService.getFeaturedProducts(limit), [limit]);
};

export const useProductsByCategory = (category, params = {}) => {
  return useApi(
    () => apiService.getProductsByCategory(category, params), 
    [category, JSON.stringify(params)]
  );
};

export const useCategories = () => {
  return useApi(() => apiService.getCategories(), []);
};

// Search hook with debouncing
export const useProductSearch = (query, delay = 300) => {
  const [debouncedQuery, setDebouncedQuery] = useState(query);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  // Perform search when debounced query changes
  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setData(null);
      setLoading(false);
      return;
    }

    const searchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiService.searchProducts(debouncedQuery);
        setData(response.data);
      } catch (err) {
        setError(err.message);
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    searchProducts();
  }, [debouncedQuery]);

  return { data, loading, error, query: debouncedQuery };
};

// Form submission hooks
export const useContactForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const submitForm = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      const response = await apiService.submitContactForm(formData);
      setSuccess(true);
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setError(null);
    setSuccess(false);
    setLoading(false);
  };

  return { submitForm, loading, error, success, reset };
};

export const useQuoteRequest = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const submitQuote = async (quoteData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      const response = await apiService.submitQuoteRequest(quoteData);
      setSuccess(true);
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setError(null);
    setSuccess(false);
    setLoading(false);
  };

  return { submitQuote, loading, error, success, reset };
};
