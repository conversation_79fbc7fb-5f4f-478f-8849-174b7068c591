import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import Button from '../components/Button';
import ProductCard from '../components/ProductCard';
import Loading, { ProductDetailSkeleton, ProductGridSkeleton } from '../components/Loading';
import { ErrorMessage } from '../components/ErrorBoundary';
import { useProduct, useProductsByCategory } from '../hooks/useApi';

const ProductDetail = () => {
  const { id } = useParams();

  // Fetch product details
  const {
    data: product,
    loading: productLoading,
    error: productError
  } = useProduct(id);

  // Fetch related products from the same category
  const {
    data: relatedProducts,
    loading: relatedLoading
  } = useProductsByCategory(
    product?.categorySlug,
    { limit: 3 }
  );

  // Filter out current product from related products
  const filteredRelatedProducts = relatedProducts?.filter(p => p.id !== product?.id) || [];

  if (productLoading) {
    return (
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <ProductDetailSkeleton />
        </div>
      </div>
    );
  }

  if (productError || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-4">
            {productError || "The product you're looking for doesn't exist."}
          </p>
          <Link to="/products" className="text-orange-500 hover:text-orange-600">
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  // Get the appropriate description (long description if available, otherwise regular description)
  const getProductDescription = (product) => {
    return product.longDescription || product.description;
  };

  const breadcrumbs = [
    { name: 'Home', href: '/' },
    { name: product.category, href: '/products' },
    { name: product.name, href: '#' }
  ];

  return (
    <div className='pt-22'>
      {/* Breadcrumb */}
      <section className="py-4 sticky top-22 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 overflow-x-auto">
              {breadcrumbs.map((item, index) => (
                <li key={item.name} className="flex items-center truncate">
                  {index > 0 && (
                    <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  {index === breadcrumbs.length - 1 ? (
                    <span className="text-[#f0f0f0] text-sm truncate">{item.name}</span>
                  ) : (
                    <Link to={item.href} className="text-[#AFAFAF] truncate hover:text-[#f0f0f0] text-sm">
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>
      </section>

      {/* Product Detail */}
      <section className="pt-16 bg-[#040A22]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Image */}
            <div>
              <div className="aspect-square border-[1px] border-[#8D91A1] rounded-xl mb-4">
                <div className="w-full h-full flex items-center justify-center">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="object-cover w-[calc(80%)] w-[calc(80%)] rounded-lg"
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400 text-sm">No Image Available</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Related Products */}
              <section className="bg-[#040A22]">
                <div className="max-w-7xl mx-auto py-5">
                  <h2 className="text-2xl font-bold text-[#FCFCFD] mb-8">Related Products</h2>
                  {relatedLoading ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
                          <div className="aspect-square bg-gray-200"></div>
                          <div className="p-4">
                            <div className="h-4 bg-gray-200 rounded mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded mb-3 w-3/4"></div>
                            <div className="flex justify-between items-center">
                              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : filteredRelatedProducts.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredRelatedProducts.map((product) => (
                        <ProductCard key={product.id} product={product} />
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center">No related products found.</p>
                  )}
                </div>
              </section>
            </div>

            {/* Product Info */}
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-[#FCFCFD] mb-4">
                {product.name}
              </h1>

              <div className="mb-6">
                <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  {product.category}
                </span>
              </div>

              <div className="mb-6">
                <p className="text-[#DADADA] leading-relaxed">
                  {getProductDescription(product).split('\n\n')[0]}
                </p>
              </div>

              {product.brand && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-[#DADADA] mb-2">Brand:</h3>
                  <p className="text-orange-500 font-medium">{product.brand}</p>
                </div>
              )}

              <div className="space-y-4 hidden">
                <Button size="lg" className="w-full sm:w-auto">
                  Contact for Pricing
                </Button>
                <Button variant="secondary" size="lg" className="w-full sm:w-auto ml-0 sm:ml-4">
                  Request Quote
                </Button>
              </div>

              {/* description */}
              <section className="bg-[#040A22]">
                <div className="mx-auto">
                  <h2 className="text-2xl font-bold text-[#FCFCFD] mb-6">Product Description</h2>
                  <div className="prose max-w-none">
                    {getProductDescription(product).split('\n\n').map((paragraph, index) => (
                      <p key={index} className="text-[#DADADA] mb-4 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </div>
              </section>

              {/* Water Parameters */}
              {product.waterParameters && (
                <section className="bg-[#040A22]">
                  <div className="max-w-7xl mx-auto">
                    <h2 className="text-2xl font-bold text-[#FCFCFD] mb-6">Water Parameters</h2>
                    <div className="bg-[#040A22] rounded-lg">
                      <div className="grid grid-cols-1 gap-8">
                        {/* Left Side Parameters */}
                        {product.waterParameters.left && (
                          <div className="bg-[#040A22] rounded-lg shadow-sm">
                            <h3 className="text-lg font-semibold text-gray-200 mb-4 border-b pb-2">Primary Parameters</h3>
                            <div className="space-y-3">
                              {Object.entries(product.waterParameters.left).map(([key, value]) => (
                                <div key={key} className="flex justify-between items-center px-3 rounded">
                                  <span className="font-medium text-gray-400 capitalize">
                                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                                  </span>
                                  <span className="text-[#f0f0f0] font-semibold">{value}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Right Side Parameters */}
                        {product.waterParameters.right && (
                          <div className="bg-[#040A22] rounded-lg shadow-sm">
                            <h3 className="text-lg font-semibold text-gray-200 mb-4 border-b pb-2">Chemical Parameters</h3>
                            <div className="space-y-3">
                              {Object.entries(product.waterParameters.right).map(([key, value]) => (
                                <div key={key} className="flex justify-between items-center px-3 rounded">
                                  <span className="font-medium text-gray-400 capitalize">
                                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                                  </span>
                                  <span className="text-[#f0f0f0] font-semibold">{value}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </section>
              )}

              {/* Specifications */}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductDetail;
