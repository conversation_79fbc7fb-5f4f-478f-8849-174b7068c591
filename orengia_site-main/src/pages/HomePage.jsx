import { Link } from 'react-router-dom';
import Button from '../components/Button';
import ProductCard from '../components/ProductCard';
import { ProductGridSkeleton } from '../components/Loading';
import { useFeaturedProducts, useCategories } from '../hooks/useApi';
import HeroImage from "../assets/images/homepage/hero/hero_img.webp";
import right_facing_arrowIcon from "../assets/icons/arrow-icon-facing-right.svg";
import knowMoreAboutImage from "../assets/images/homepage/know-more-about/know-more.webp";
import shrimpImage from "../assets/images/homepage/3rd-sec/shrimp.webp";
import qualityIcon from "../assets/icons/quality_icon.png";
import customerIcon from "../assets/icons/customer_icon.png";
import supplyIcon from "../assets/icons/supply_icon.png";
import expartIcon from "../assets/icons/expart_icon.png";
import shrimpImg from "../assets/images/homepage/exp/shrimp.png";

const signatureButton = ({ name, href, onClick }) => {
  return (
    <Link
      to={href}
      onClick={onClick}
      className="text-base flex items-center px-4 p-2 w-fit font-medium rounded-sm transition-all duration-200 bg-orange-500 text-white hover:bg-orange-600 gap-3 hover:gap-5"
    >
      {name}
      <img className='w-4 h-4' src={right_facing_arrowIcon} alt="Search" />
    </Link>
  );
}

const signatureParagraph = ({ text }) => {
  return (
    <div className='w-fit w-fit flex flex-col pl-2 relative'>
      <div className='absolute w-[0.2rem] rounded-full h-11 bg-[#F66408] top-1 -left-2'></div>
      <p className="text-[#f0f0f0] mb-6 leading-relaxed">
        {text}
      </p>
    </div>
  );
}

const signatureParagraphWithButton = ({ text, name, href, onClick }) => {
  return (
    <div className='w-fit w-fit flex flex-col pl-2 relative'>
      <div className='absolute w-[0.2rem] rounded-full h-11 bg-[#F66408] top-1 -left-2'></div>
      <p className="text-[#f0f0f0] mb-6 leading-relaxed">
        {text}
      </p>

      <div className='pt-13'>
        {signatureButton({
          name: name,
          href: href,
          onClick: onClick
        })}
      </div>
    </div>
  );
}

const heroCategoryCard = ({ category }) => {
  return (
    <Link to={`/products?category=${category.slug}`} className='max-w-2xl flex flex-col gap-4 px-4 sm:px-6 lg:px-8 hover:scale-105 transition-transform duration-200'>
      <div className='w-fit h-fit flex flex-col items-center justify-center gap-3'>
        <div className='w-42 h-42 rounded-full bg-white border-7 border-[#F66408] overflow-hidden flex items-center justify-center'>
          {category.thumbnail ? (
            <img
              src={category.thumbnail}
              alt={category.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.style.display = 'none';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-400 text-sm">No Image</span>
            </div>
          )}
        </div>
        <div className='max-w-xl flex flex-col items-center gap-1'>
          <p className='text-[#f0f0f0] text-2xl text-center font-medium'>{category.name}</p>
          <p className='text-[#f0f0f0] text-sm font-light max-w-[10rem] text-center'>
            {category.description}
          </p>
        </div>
      </div>
    </Link>
  )
}

const experienceCard = ({icon, title, description }) => {
  return (
    <div className="w-full h-auto border-1 border-[#313C68] rounded-md">
      <div className='w-full h-auto flex flex-col p-3 gap-3'>
        <div className='w-full h-7'>
          <img className='w-7 h-7' src={icon} alt="quality" />
        </div>
        <p className='text-[#f0f0f0] text-md font-medium'>{title}</p>
        <p className='text-[#9D9D9D] text-sm font-light pr-3 pb-5'>
          {description}
        </p>
      </div>
    </div>
  )
}

const HomePage = () => {
  // Fetch featured products and categories
  const {
    data: featuredProducts,
    loading: featuredLoading,
    error: featuredError
  } = useFeaturedProducts(4);

  const {
    data: categories,
    loading: categoriesLoading
  } = useCategories();

  // Get featured categories for hero section
  const featuredCategories = categories?.filter(cat => cat.featured) || [];

  return (
    <div>
      {/* Hero Section */}
      <section className="min-h-screen h-[100vh] w-full flex flex-col items-center">
        <div className="w-full h-full">
          <div className='w-full h-full'>
            <div className='w-full h-full relative'>
              <div className='absolute w-full h-full z-10'>
                <div className='w-full h-full relative flex flex-col items-center'>
                  {/* Hero Content */}
                  <div className='absolute py-2 max-w-7xl w-full h-fit top-[40%] translate-y-[-50%]'>
                    <div className='max-w-2xl flex flex-col gap-4 px-4 sm:px-6 lg:px-8'>
                      <p className='text-[#f0f0f0] text-4xl md:text-5xl font-bold'>A Trusted Supplier For
                        <span className='text-[#E86009]'> Feed</span>
                      </p>
                      <p className='text-[#f0f0f0] text-md font-normal max-w-[20rem]'>Suppliers of feed for freshwater and saltwater aquaculture</p>
                      <div className='pt-13'>
                        {signatureButton({
                          name: "Explore Our Products",
                          href: "/products",
                          onClick: () => { }
                        })}
                      </div>
                    </div>
                  </div>

                  <div style={{
                    // noscrollbar
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none'
                  }} className='absolute py-2 max-w-7xl w-full h-fit top-[83%] translate-y-[-50%] flex flex-row justify-between overflow-x-auto gap-5'>

                    {/* Featured Categories */}
                    {categoriesLoading ? (
                      // Loading skeleton
                      Array.from({ length: 3 }).map((_, index) => (
                        <div key={index} className='max-w-2xl flex flex-col gap-4 px-4 sm:px-6 lg:px-8'>
                          <div className='w-fit h-fit flex flex-col items-center justify-center gap-3'>
                            <div className='w-42 h-42 rounded-full bg-gray-300 animate-pulse'></div>
                            <div className='max-w-xl flex flex-col items-center gap-1'>
                              <div className='h-6 bg-gray-300 rounded w-32 animate-pulse'></div>
                              <div className='h-4 bg-gray-300 rounded w-24 animate-pulse'></div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : featuredCategories.length > 0 ? (
                      featuredCategories.map((category) => (
                        heroCategoryCard({ key: category.id, category })
                      ))
                    ) : (
                      // Fallback if no featured categories
                      <div className='max-w-2xl flex flex-col gap-4 px-4 sm:px-6 lg:px-8'>
                        <div className='w-fit h-fit flex flex-col items-center justify-center gap-3'>
                          <div className='w-42 h-42 rounded-full bg-white border-7 border-[#F66408] flex items-center justify-center'>
                            <span className="text-gray-400">No Categories</span>
                          </div>
                          <div className='max-w-xl flex flex-col items-center gap-1'>
                            <p className='text-[#f0f0f0] text-2xl font-medium'>Browse Products</p>
                            <p className='text-[#f0f0f0] text-sm font-light max-w-[10rem] text-center'>
                              Explore our quality aquaculture supplies
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                </div>
              </div>
              {/* Background */}
              <div className='absolute w-full h-full'>
                <div className='w-full h-full relative'>
                  <div className='absolute w-full h-[83vh]'>
                    <img className='w-full h-full object-cover' src={HeroImage} alt="" />
                  </div>
                  <div className='absolute w-full h-[17vh] bottom-0 bg-[#020616] opacity-20'></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Know More About Section */}
      <section className="py-16 md:py-20 transition-all duration-200 lg:pt-40 bg-[#040A22]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className='lg:w-[30rem] lg:h-[20rem]'>
              <h2 className="text-3xl md:text-4xl font-medium text-[#f0f0f0] mb-6 text-center">
                Know More About<br /><span className='text-[#F66408]'>Orangeya</span>
              </h2>
              <img className='w-full h-[17rem] object-cover rounded-lg mb-3 lg:block' src={knowMoreAboutImage} alt="know-more-about" />
              <div className='block lg:hidden'>
                {signatureParagraphWithButton({
                  text: "At Orangeya, integrity is at the heart of everything we do. Established in 1995 an industry, we set out to redefine the standards of excellence. With a steadfast commitment to honesty and purpose, Orangeya has become a trusted importer and distributor of shrimp hatchery inputs, proudly representing Ocean Star International Inc. Our dedication to quality and innovation has not only earned us a distinguished reputation but has also reshaped industry standards, making us a leading force in the field. Join us as we continue to empower growth and inspire confidence in the shrimp hatchery industry and beyond.",
                  name: "Learn More",
                  href: "/about",
                  onClick: () => { }
                })}
              </div>
            </div>


            <div className="grid w-full grid-cols-2 hidden lg:block">
              <div>
                {signatureParagraphWithButton({
                  text: "At Orangeya, integrity is at the heart of everything we do. Established in 1995 an industry, we set out to redefine the standards of excellence. With a steadfast commitment to honesty and purpose, Orangeya has become a trusted importer and distributor of shrimp hatchery inputs, proudly representing Ocean Star International Inc. Our dedication to quality and innovation has not only earned us a distinguished reputation but has also reshaped industry standards, making us a leading force in the field. Join us as we continue to empower growth and inspire confidence in the shrimp hatchery industry and beyond.",
                  name: "Learn More",
                  href: "/about",
                  onClick: () => { }
                })}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Your Trusted Partner for Brine Shrimp Eggs */}
      <section className="py-16 md:py-20 transition-all duration-200 lg:pt-40 bg-[#040A22]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-medium text-[#f0f0f0] mb-6">
                Your Trusted Partner for<br /><span className='text-[#F66408]'>Brine Shrimp Eggs</span>
              </h2>
              <img className='w-full h-[17rem] object-cover rounded-lg mb-3 lg:hidden' src={shrimpImage} alt="shrimp" />
              {signatureParagraphWithButton({
                text: "At Orangeya, integrity is at the heart of everything we do. Established in 1995 an industry, we set out to redefine the standards of excellence. With a steadfast commitment to honesty and purpose, Orangeya has become a trusted importer and distributor of shrimp hatchery inputs, proudly representing Ocean Star International Inc. Our dedication to quality and innovation has not only earned us a distinguished reputation but has also reshaped industry standards, making us a leading force in the field. Join us as we continue to empower growth and inspire confidence in the shrimp hatchery industry and beyond.",
                name: "View More",
                href: "/products",
                onClick: () => { }
              })}
            </div>



            <div className="grid w-full grid-cols-2 hidden lg:block">
              <div className='w-[30rem] h-fit'>
                <img className='w-full h-fit object-cover rounded-md' src={shrimpImage} alt="know-more-about" />
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Experience Excellence in Aquaculture Supplies */}
      <section className="py-16 md:py-20 transition-all duration-200 lg:pt-40 bg-[#040A22]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-medium text-[#f0f0f0] mb-6">
                Experience Excellence in <br /><span className='text-[#F66408]'>Aquaculture Supplies</span>
              </h2>
              {signatureParagraph({
                text: "At Orangeya , we provide unparalleled expertise and top-quality products to meet the unique needs of shrimp hatcheries. With a strong commitment to sustainability and customer satisfaction, we ensure reliable solutions that foster growth and success in the aquaculture industry."
              })}
            </div>

            <div className="grid w-full lg:block">
              <div className="grid grid-cols-2 gap-4">
                {experienceCard({
                  icon: qualityIcon,
                  title: "Quality Products",
                  description: "We offer a wide range of high-quality Artemia products, including cysts, hatchery equipment, and specialized feeds, ensuring optimal growth and health for your shrimp."
                })}
                {experienceCard({
                  icon: expartIcon,
                  title: "Expert Guidance",
                  description: "With years of experience in the industry, our team offers expert advice and support to optimize your Artemia cultivation and feeding practices."
                })}
                {experienceCard({
                  icon: supplyIcon,
                  title: "Reliable Supply",
                  description: "We understand the importance of timely delivery. Count on us for prompt and reliable supply of products to meet your needs."
                })}
                {experienceCard({
                  icon: customerIcon,
                  title: "Customer Satisfaction",
                  description: "Your satisfaction is our priority. We strive to exceed your expectations with exceptional products and personalized service."
                })}
              </div>
            </div>

            <div className='absolute hidden md:block left-[20%]'>
              <img className='w-[30rem] h-auto' src={shrimpImg} alt="" />
            </div>
          </div>
          <p className="text-2xl md:text-2xl font-medium text-[#f0f0f0] text-center pt-20">
                Check Out Our<br /><span className='text-[#F66408] text-3xl'>Quality Supplies</span>
              </p>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-[#060E2C]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-xl md:text-2xl font-medium text-[#f0f0f0] mb-4">
              Artemia Cysts
            </h2>
            <p className="text-[#FCFCFD] max-w-[17rem] md:max-w-3xl mx-auto">
              Our high-quality Artemia cysts are a premium product designed to meet the nutritional needs of various aquatic species in hatcheries and aquaculture operations. Artemia, commonly known as brine shrimp, are an essential live feed for shrimp, fish larvae, and other marine organisms due to their rich nutritional profile and ease of use.
            </p>
          </div>

          {featuredLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
                  <div className="aspect-square bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-3 w-3/4"></div>
                    <div className="flex justify-between items-center">
                      <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : featuredError ? (
            <div className="text-center py-8 mb-8">
              <p className="text-red-600">Failed to load featured products</p>
            </div>
          ) : featuredProducts && featuredProducts.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-20 mb-8">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 mb-8">
              <p className="text-gray-500">No featured products available</p>
            </div>
          )}

          <div className="text-center">
            <Link to="/products">
              <Button variant="secondary">View All Products</Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
