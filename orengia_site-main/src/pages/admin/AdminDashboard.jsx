import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useProducts, useCategories } from '../../hooks/useApi';

const AdminDashboard = () => {
  const { data: products, loading: productsLoading } = useProducts();
  const { data: categories, loading: categoriesLoading } = useCategories();

  const stats = {
    totalProducts: products?.length || 0,
    featuredProducts: products?.filter(p => p.featured).length || 0,
    totalCategories: categories?.length || 0,
    featuredCategories: categories?.filter(c => c.featured).length || 0,
  };

  const quickActions = [
    {
      title: 'Add New Product',
      description: 'Create a new product listing',
      href: '/admin/products/new',
      icon: '📦',
      color: 'bg-blue-500'
    },
    {
      title: 'Manage Products',
      description: 'View and edit existing products',
      href: '/admin/products',
      icon: '📋',
      color: 'bg-green-500'
    },
    {
      title: 'Manage Categories',
      description: 'Organize product categories',
      href: '/admin/categories',
      icon: '📂',
      color: 'bg-purple-500'
    },
    {
      title: 'Featured Items',
      description: 'Manage featured products and categories',
      href: '/admin/featured',
      icon: '⭐',
      color: 'bg-orange-500'
    }
  ];

  return (
    <div className="space-y-6 bg-[#060E2C]">
      {/* Header */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-200">Admin Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome to Orangeya Administration Panel</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 text-[#f0f0f0] md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-[#060E2C] overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">📦</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Products
                  </dt>
                  <dd className="text-lg font-medium text-gray-200">
                    {productsLoading ? '...' : stats.totalProducts}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-[#060E2C] overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">⭐</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Featured Products
                  </dt>
                  <dd className="text-lg font-medium text-gray-200">
                    {productsLoading ? '...' : stats.featuredProducts}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-[#060E2C] overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">📂</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Categories
                  </dt>
                  <dd className="text-lg font-medium text-gray-200">
                    {categoriesLoading ? '...' : stats.totalCategories}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-[#060E2C] overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">🏷️</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Featured Categories
                  </dt>
                  <dd className="text-lg font-medium text-gray-200">
                    {categoriesLoading ? '...' : stats.featuredCategories}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-200 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.href}
              className="block p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-center">
                <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center text-white text-lg mr-3`}>
                  {action.icon}
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-200">{action.title}</h3>
                  <p className="text-xs text-gray-500 mt-1">{action.description}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-200 mb-4">Recent Activity</h2>
        <div className="text-sm text-gray-500">
          <p>• System initialized</p>
          <p>• Admin panel ready</p>
          <p>• {stats.totalProducts} products loaded</p>
          <p>• {stats.totalCategories} categories configured</p>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
