import { useState } from 'react';
import { useProducts, useCategories } from '../../hooks/useApi';
import apiService from '../../services/api';

const FeaturedManagement = () => {
  const { data: products, loading: productsLoading, refetch: refetchProducts } = useProducts();
  const { data: categories, loading: categoriesLoading, refetch: refetchCategories } = useCategories();

  const [activeTab, setActiveTab] = useState('products');

  const featuredProducts = products?.filter(p => p.featured) || [];
  const featuredCategories = categories?.filter(c => c.featured) || [];

  const toggleProductFeatured = async (productId, currentFeatured) => {
    try {
      await apiService.toggleProductFeatured(productId);
      refetchProducts();
    } catch (error) {
      console.error('Error toggling product featured status:', error);
      alert('Failed to update product featured status. Please try again.');
    }
  };

  const toggleCategoryFeatured = async (categoryId, currentFeatured) => {
    try {
      await apiService.toggleCategoryFeatured(categoryId);
      refetchCategories();
    } catch (error) {
      console.error('Error toggling category featured status:', error);
      alert('Failed to update category featured status. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-200">Featured Items Management</h1>
        <p className="text-gray-400 mt-1">Manage which products and categories appear on the homepage</p>
      </div>

      {/* Tabs */}
      <div className="bg-[#060E2C] shadow rounded-lg">
        <div className="border-b border-gray-500">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('products')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'products'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Featured Products ({featuredProducts.length})
            </button>
            <button
              onClick={() => setActiveTab('categories')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'categories'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Featured Categories ({featuredCategories.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'products' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-200">Featured Products</h2>
                <p className="text-sm text-gray-500">
                  These products will appear in the homepage featured section
                </p>
              </div>

              {productsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading products...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Featured Products */}
                  {featuredProducts.length > 0 && (
                    <div>
                      <h3 className="text-md font-medium text-gray-200 mb-3">Currently Featured</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {featuredProducts.map((product) => (
                          <div key={product.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center space-x-3">
                              <img
                                src={product.thumbnail || product.image || '/placeholder.jpg'}
                                alt={product.name}
                                className="w-12 h-12 rounded-lg object-cover"
                                onError={(e) => {
                                  e.target.src = '/placeholder.jpg';
                                }}
                              />
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-200">{product.name}</h4>
                                <p className="text-xs text-gray-500">{product.category}</p>
                              </div>
                              <button
                                onClick={() => toggleProductFeatured(product.id, product.featured)}
                                className="text-yellow-600 hover:text-yellow-800"
                                title="Remove from featured"
                              >
                                ⭐
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* All Products */}
                  <div>
                    <h3 className="text-md font-medium text-gray-200 mb-3">All Products</h3>
                    <div className="max-h-96 overflow-y-auto">
                      <div className="space-y-2">
                        {products?.map((product) => (
                          <div key={product.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <img
                                src={product.thumbnail || product.image || '/placeholder.jpg'}
                                alt={product.name}
                                className="w-10 h-10 rounded-lg object-cover"
                                onError={(e) => {
                                  e.target.src = '/placeholder.jpg';
                                }}
                              />
                              <div>
                                <h4 className="text-sm font-medium text-gray-200">{product.name}</h4>
                                <p className="text-xs text-gray-500">{product.category}</p>
                              </div>
                            </div>
                            <button
                              onClick={() => toggleProductFeatured(product.id, product.featured)}
                              className={`px-3 py-1 text-xs font-medium rounded-full ${
                                product.featured
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                              }`}
                            >
                              {product.featured ? '⭐ Featured' : 'Add to Featured'}
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'categories' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-200">Featured Categories</h2>
                <p className="text-sm text-gray-500">
                  These categories will appear in the homepage hero section
                </p>
              </div>

              {categoriesLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading categories...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Featured Categories */}
                  {featuredCategories.length > 0 && (
                    <div>
                      <h3 className="text-md font-medium text-gray-200 mb-3">Currently Featured</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {featuredCategories.map((category) => (
                          <div key={category.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center space-x-3">
                              <img
                                src={category.thumbnail || '/placeholder.jpg'}
                                alt={category.name}
                                className="w-12 h-12 rounded-lg object-cover"
                                onError={(e) => {
                                  e.target.src = '/placeholder.jpg';
                                }}
                              />
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-200">{category.name}</h4>
                                <p className="text-xs text-gray-500">{category.productCount} products</p>
                              </div>
                              <button
                                onClick={() => toggleCategoryFeatured(category.id, category.featured)}
                                className="text-yellow-600 hover:text-yellow-800"
                                title="Remove from featured"
                              >
                                ⭐
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* All Categories */}
                  <div>
                    <h3 className="text-md font-medium text-gray-200 mb-3">All Categories</h3>
                    <div className="space-y-2">
                      {categories?.map((category) => (
                        <div key={category.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <img
                              src={category.thumbnail || '/placeholder.jpg'}
                              alt={category.name}
                              className="w-10 h-10 rounded-lg object-cover"
                              onError={(e) => {
                                e.target.src = '/placeholder.jpg';
                              }}
                            />
                            <div>
                              <h4 className="text-sm font-medium text-gray-200">{category.name}</h4>
                              <p className="text-xs text-gray-500">{category.productCount} products</p>
                            </div>
                          </div>
                          <button
                            onClick={() => toggleCategoryFeatured(category.id, category.featured)}
                            className={`px-3 py-1 text-xs font-medium rounded-full ${
                              category.featured
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }`}
                          >
                            {category.featured ? '⭐ Featured' : 'Add to Featured'}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FeaturedManagement;
