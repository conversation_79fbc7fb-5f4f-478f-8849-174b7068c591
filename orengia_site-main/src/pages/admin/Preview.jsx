import { useState } from 'react';
import { useProducts, useCategories } from '../../hooks/useApi';
import apiService from '../../services/api';

const Preview = () => {

  return (
    <div className="space-y-6 fixed w-full">
      {/* Header */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-200">Preview Site Content</h1>
        <p className="text-gray-400 mt-1">Preview the site content before publishing</p>
      </div>

      {/* Iframe */}
      <div className="bg-[#060E2C] shadow">
       <div class="grid grid-cols-2 gap-4">
          <div class="max-w-md h-[30vh]">
            <iframe style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }} class="w-full h-[80vh] scale-[80%] rounded-xl hover:scale-[83%] transition-all duration-300 translate-x-[20%] translate-y-[-10%]" src="https://7lxpwrn1-5173.asse.devtunnels.ms/" title="Site Preview" className="w-full h-[80vh]"></iframe>
          </div>
          <div className='col-span-1 flex items-center justify-center'>
            <iframe style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }} class="w-full h-[80vh] scale-[80%] rounded-xl hover:scale-[83%] transition-all duration-300 translate-x-[20%] translate-y-[-10%]" src="https://7lxpwrn1-5173.asse.devtunnels.ms/" title="Site Preview" className="w-full h-[80vh]"></iframe>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preview;
