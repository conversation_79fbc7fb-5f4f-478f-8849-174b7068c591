import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProduct, useCategories } from '../../hooks/useApi';
import apiService from '../../services/api';
import Button from '../../components/Button';
import ImageUpload from '../../components/ImageUpload';

const ProductForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = Boolean(id);

  const { data: product, loading: productLoading } = useProduct(id);
  const { data: categories } = useCategories();

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    category: '',
    categorySlug: '',
    description: '',
    longDescription: '',
    brand: '',
    price: '',
    inStock: true,
    featured: false,
    image: '',
    thumbnail: '',
    coverImage: '',
    gallery: [],
    specifications: {},
    waterParameters: {
      left: {},
      right: {}
    },
    tags: []
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isEditing && product) {
      setFormData({
        ...product,
        price: product.price || '',
        gallery: product.gallery || [],
        tags: Array.isArray(product.tags) ? product.tags.join(', ') : ''
      });
    }
  }, [isEditing, product]);

  const generateSlug = (name) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === 'name') {
      setFormData({
        ...formData,
        [name]: value,
        slug: generateSlug(value)
      });
    } else if (name === 'category') {
      const selectedCategory = categories?.find(cat => cat.name === value);
      setFormData({
        ...formData,
        category: value,
        categorySlug: selectedCategory?.slug || ''
      });
    } else {
      setFormData({
        ...formData,
        [name]: type === 'checkbox' ? checked : value
      });
    }
  };

  const handleSpecificationChange = (key, value) => {
    setFormData({
      ...formData,
      specifications: {
        ...formData.specifications,
        [key]: value
      }
    });
  };

  const addSpecification = () => {
    const key = prompt('Enter specification name:');
    if (key) {
      handleSpecificationChange(key, '');
    }
  };

  const removeSpecification = (key) => {
    const newSpecs = { ...formData.specifications };
    delete newSpecs[key];
    setFormData({
      ...formData,
      specifications: newSpecs
    });
  };

  const handleWaterParameterChange = (side, key, value) => {
    setFormData({
      ...formData,
      waterParameters: {
        ...formData.waterParameters,
        [side]: {
          ...formData.waterParameters[side],
          [key]: value
        }
      }
    });
  };

  const addWaterParameter = (side) => {
    const key = prompt(`Enter ${side} water parameter name:`);
    if (key) {
      handleWaterParameterChange(side, key, '');
    }
  };

  const removeWaterParameter = (side, key) => {
    const newParams = { ...formData.waterParameters[side] };
    delete newParams[key];
    setFormData({
      ...formData,
      waterParameters: {
        ...formData.waterParameters,
        [side]: newParams
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const submitData = {
        ...formData,
        tags: typeof formData.tags === 'string'
          ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
          : formData.tags,
        price: formData.price || null,
        gallery: Array.isArray(formData.gallery) ? formData.gallery : [],
        createdAt: isEditing ? product.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      if (isEditing) {
        await apiService.updateProduct(id, submitData);
      } else {
        await apiService.createProduct(submitData);
      }

      navigate('/admin/products');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (isEditing && productLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-[#060E2C] shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-200">
          {isEditing ? 'Edit Product' : 'Add New Product'}
        </h1>
        <p className="text-gray-400 mt-1">
          {isEditing ? 'Update product information' : 'Create a new product listing'}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6 text-gray-200 ">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Basic Information */}
        <div className="bg-[#060E2C] shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-200 mb-4">Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Slug
              </label>
              <input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Category *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Select Category</option>
                {categories?.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Brand
              </label>
              <input
                type="text"
                name="brand"
                value={formData.brand}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Price
              </label>
              <input
                type="text"
                name="price"
                value={formData.price}
                onChange={handleChange}
                placeholder="Leave empty for quote-based pricing"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Tags (comma-separated)
              </label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                placeholder="premium, artemia, high-protein"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Short Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            />
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Long Description
            </label>
            <textarea
              name="longDescription"
              value={formData.longDescription}
              onChange={handleChange}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            />
          </div>

          <div className="mt-6 flex items-center space-x-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="inStock"
                checked={formData.inStock}
                onChange={handleChange}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-400">In Stock</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleChange}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-400">Featured Product</span>
            </label>
          </div>
        </div>

        {/* Specifications */}
        <div className="bg-[#060E2C] shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-200 mb-4">Specifications</h2>
          
          <div className="space-y-4">
            {Object.entries(formData.specifications || {}).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-3">
                <input
                  type="text"
                  value={key}
                  readOnly
                  className="w-1/3 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
                <input
                  type="text"
                  value={value}
                  onChange={(e) => handleSpecificationChange(key, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Enter specification value"
                />
                <button
                  type="button"
                  onClick={() => removeSpecification(key)}
                  className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                  Remove
                </button>
              </div>
            ))}
            
            <button
              type="button"
              onClick={addSpecification}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Add Specification
            </button>
          </div>
        </div>

        {/* Water Parameters */}
        <div className="bg-[#060E2C] shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-200 mb-4">Water Parameters</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Side Parameters */}
            <div>
              <h3 className="text-md font-medium text-gray-300 mb-3">Primary Parameters</h3>
              <div className="space-y-4">
                {Object.entries(formData.waterParameters?.left || {}).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={key}
                      readOnly
                      className="w-1/3 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 text-sm"
                    />
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => handleWaterParameterChange('left', key, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
                      placeholder="e.g., 24-28°C"
                    />
                    <button
                      type="button"
                      onClick={() => removeWaterParameter('left', key)}
                      className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                ))}
                
                <button
                  type="button"
                  onClick={() => addWaterParameter('left')}
                  className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                >
                  Add Primary Parameter
                </button>
              </div>
            </div>

            {/* Right Side Parameters */}
            <div>
              <h3 className="text-md font-medium text-gray-300 mb-3">Chemical Parameters</h3>
              <div className="space-y-4">
                {Object.entries(formData.waterParameters?.right || {}).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={key}
                      readOnly
                      className="w-1/3 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 text-sm"
                    />
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => handleWaterParameterChange('right', key, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
                      placeholder="e.g., ≥5 mg/L"
                    />
                    <button
                      type="button"
                      onClick={() => removeWaterParameter('right', key)}
                      className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                ))}
                
                <button
                  type="button"
                  onClick={() => addWaterParameter('right')}
                  className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                >
                  Add Chemical Parameter
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Images */}
        <div className="bg-[#060E2C] shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-200 mb-4">Product Images</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ImageUpload
              label="Main Product Image"
              value={formData.image}
              onChange={(value) => setFormData({...formData, image: value})}
              placeholder="/src/assets/images/products/..."
            />

            <ImageUpload
              label="Thumbnail Image"
              value={formData.thumbnail}
              onChange={(value) => setFormData({...formData, thumbnail: value})}
              placeholder="/src/assets/images/products/...-thumb.jpg"
            />

            <ImageUpload
              label="Cover Image"
              value={formData.coverImage}
              onChange={(value) => setFormData({...formData, coverImage: value})}
              placeholder="/src/assets/images/products/...-cover.jpg"
            />

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Gallery Images (comma-separated URLs)
              </label>
              <textarea
                name="gallery"
                value={Array.isArray(formData.gallery) ? formData.gallery.join(', ') : formData.gallery}
                onChange={(e) => setFormData({
                  ...formData,
                  gallery: e.target.value.split(',').map(url => url.trim()).filter(Boolean)
                })}
                rows={3}
                placeholder="/src/assets/images/products/...-1.jpg, /src/assets/images/products/...-2.jpg"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate('/admin/products')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
