import { useState } from 'react';
import ProductCard from '../components/ProductCard';
import Loading, { ProductGridSkeleton } from '../components/Loading';
import { ErrorMessage } from '../components/ErrorBoundary';
import { useProducts, useCategories } from '../hooks/useApi';

const Products = ({categorie}) => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Fetch products and categories using API hooks
  const {
    data: products,
    loading: productsLoading,
    error: productsError,
    refetch: refetchProducts
  } = useProducts({ category: selectedCategory });

  const {
    data: categories,
    loading: categoriesLoading,
    error: categoriesError
  } = useCategories();

  // Handle category change
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  // Prepare categories for display (add "All" option)
  const displayCategories = categories ? [
    { id: 0, name: 'All', slug: 'all' },
    ...categories
  ] : [];

  return (
    <div className='mt-22'>

      {/* Category Filter */}
      <section className="py-8 hidden bg-[#040A22] border-b sticky top-22 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {categoriesLoading ? (
            <div className="flex justify-center">
              <Loading size="sm" text="Loading categories..." />
            </div>
          ) : categoriesError ? (
            <div className="text-center text-red-600">
              Error loading categories
            </div>
          ) : (
            <div className="flex flex-wrap gap-2 justify-center">
              {displayCategories.map((category) => (
                <button
                  key={category.slug}
                  onClick={() => handleCategoryChange(category.slug)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                    selectedCategory === category.slug
                      ? 'bg-orange-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {productsLoading ? (
            <ProductGridSkeleton count={8} />
          ) : productsError ? (
            <ErrorMessage
              error={productsError}
              onRetry={refetchProducts}
              title="Failed to load products"
            />
          ) : products && products.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No products found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Subscription Section */}
      <section className="py-16 bg-navy-200 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Brine Shrimp Flakes
              </h2>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-200 rounded mr-4"></div>
                    <div>
                      <h3 className="font-semibold text-gray-800">Crystal</h3>
                      <p className="text-gray-600 text-sm">Premium quality flakes</p>
                    </div>
                  </div>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded font-medium hover:bg-orange-600 transition-colors">
                    Order
                  </button>
                </div>

                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-200 rounded mr-4"></div>
                    <div>
                      <h3 className="font-semibold text-gray-800">Five Star</h3>
                      <p className="text-gray-600 text-sm">High-grade quality</p>
                    </div>
                  </div>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded font-medium hover:bg-orange-600 transition-colors">
                    Order
                  </button>
                </div>

                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-200 rounded mr-4"></div>
                    <div>
                      <h3 className="font-semibold text-gray-800">Red Jungle (RJB) Pro 80</h3>
                      <p className="text-gray-600 text-sm">Professional grade</p>
                    </div>
                  </div>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded font-medium hover:bg-orange-600 transition-colors">
                    Order
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div className="aspect-square bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscription Flakes */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8 text-center">
            Subscription Flakes
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {['Artemia & Spirulina', 'Spirulina', 'Fish', 'Artemia'].map((item, index) => (
              <div key={index} className="text-center">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg mb-4"></div>
                <h3 className="font-semibold text-gray-800">{item}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Aquaculture Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="grid grid-cols-2 gap-4">
              <div className="aspect-square bg-gradient-to-br from-green-100 to-green-200 rounded-lg"></div>
              <div className="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg"></div>
              <div className="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg"></div>
              <div className="aspect-square bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg"></div>
            </div>

            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                Aquaculture <span className="text-orange-500">Solutions & Supplements</span>
              </h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Comprehensive range of aquaculture solutions including feeds, supplements, and specialized products
                designed to enhance the growth and health of your marine livestock.
              </p>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  <span className="text-gray-700">Premium quality ingredients</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  <span className="text-gray-700">Scientifically formulated</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  <span className="text-gray-700">Proven results</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Products;
