// API Base Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
};

// Real API service using fetch
class ApiService {
  // Generic API call method
  async makeRequest(endpoint, options = {}) {
    const { method = 'GET', body, params } = options;

    // Build URL with query parameters
    let url = `${API_CONFIG.baseURL}${endpoint}`;
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
      url += `?${searchParams.toString()}`;
    }

    const fetchOptions = {
      method,
      headers: API_CONFIG.headers,
      signal: AbortSignal.timeout(API_CONFIG.timeout),
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      fetchOptions.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, fetchOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw new Error(`API Error: ${error.message}`);
    }
  }

  // Public API methods
  async getProducts(params = {}) {
    return await this.makeRequest('/products', { params });
  }

  async getProductById(id) {
    return await this.makeRequest(`/products/${id}`);
  }

  async getCategories() {
    return await this.makeRequest('/categories');
  }

  async getFeaturedProducts(limit = 4) {
    return await this.makeRequest('/products', {
      params: { featured: true, limit }
    });
  }

  async getProductsByCategory(category, params = {}) {
    return await this.makeRequest('/products', {
      params: { ...params, category }
    });
  }

  async searchProducts(query, params = {}) {
    return await this.makeRequest('/products', {
      params: { ...params, search: query }
    });
  }

  // Contact form submission
  async submitContactForm(formData) {
    return await this.makeRequest('/contact', {
      method: 'POST',
      body: formData
    });
  }

  // Quote request submission
  async submitQuoteRequest(quoteData) {
    return await this.makeRequest('/quote', {
      method: 'POST',
      body: quoteData
    });
  }

  // Admin API methods

  // Product management
  async createProduct(productData) {
    return await this.makeRequest('/admin/products', {
      method: 'POST',
      body: productData
    });
  }

  async updateProduct(id, productData) {
    return await this.makeRequest(`/admin/products/${id}`, {
      method: 'PUT',
      body: productData
    });
  }

  async deleteProduct(id) {
    return await this.makeRequest(`/admin/products/${id}`, {
      method: 'DELETE'
    });
  }

  async toggleProductFeatured(id) {
    return await this.makeRequest(`/admin/products/${id}/featured`, {
      method: 'PATCH'
    });
  }

  // Category management
  async createCategory(categoryData) {
    return await this.makeRequest('/admin/categories', {
      method: 'POST',
      body: categoryData
    });
  }

  async updateCategory(id, categoryData) {
    return await this.makeRequest(`/admin/categories/${id}`, {
      method: 'PUT',
      body: categoryData
    });
  }

  async deleteCategory(id) {
    return await this.makeRequest(`/admin/categories/${id}`, {
      method: 'DELETE'
    });
  }

  async toggleCategoryFeatured(id) {
    return await this.makeRequest(`/admin/categories/${id}/featured`, {
      method: 'PATCH'
    });
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;

// Named exports for convenience
export const {
  getProducts,
  getProductById,
  getCategories,
  getFeaturedProducts,
  getProductsByCategory,
  searchProducts,
  submitContactForm,
  submitQuoteRequest
} = apiService;
