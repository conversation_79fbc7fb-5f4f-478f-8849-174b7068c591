<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" version="1.1" width="595.2756" height="595.2756" viewBox="0 0 595.2756 595.2756">
<g inkscape:groupmode="layer" inkscape:label="Layer 1">
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M0 595.2756H595.2756V0H0Z" fill="#081f3b" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" stroke-width=".5669" stroke-linecap="butt" stroke-miterlimit="22.925585" stroke-linejoin="miter" fill="none" stroke="#1a1a18" d="M0 595.2756H595.2756V0H0Z"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M363.2581 466.6192C360.4071 462.973 359.769 453.892 373.4116 464.0786 389.1268 475.8129 404.1232 504.5406 403.2995 499.0697 398.4313 466.7377 364.1221 453.2893 357.7184 431.0649 351.8813 410.8065 325.7133 383.4397 299.5931 383.4397 276.5259 383.4397 256.468 402.7476 256.468 426.5649 256.468 438.6396 261.5145 449.4708 269.4277 457.384 285.3652 473.3215 326.281 472.7911 359.9977 477.39 392.8867 481.8762 375.4661 482.2316 363.2581 466.6192ZM334.1582 469.1228C341.6142 469.1228 347.6582 463.0788 347.6582 455.6228 347.6582 448.1668 341.6142 442.1228 334.1582 442.1228 326.7023 442.1228 320.6582 448.1668 320.6582 455.6228 320.6582 463.0788 326.7023 469.1228 334.1582 469.1228" fill="#f5782e" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M456.4128 517.0876C455.8759 518.6537 456.7102 520.3593 458.2763 520.8962 459.8425 521.4331 461.5481 520.5988 462.0849 519.0327 473.2543 486.4895 459.5145 475.6734 438.9083 468.6009 434.2711 467.0093 429.1265 465.5903 423.8447 464.1338 411.5086 460.732 398.3862 457.1118 388.1353 449.9467 389.5821 449.5822 391.0453 449.1247 392.5582 448.6516 400.0294 446.3144 408.9339 443.5296 422.0141 452.8723 426.6193 456.1492 430.5555 451.6064 425.4831 447.9973 401.3365 430.7502 389.4528 445.5079 380.122 443.994 374.5953 443.0971 375.6745 447.071 378.4246 449.7165 390.2632 461.0781 406.8431 465.6506 422.2511 469.8995 427.3294 471.2998 432.2758 472.6641 436.9632 474.2731 454.3418 480.2377 465.9157 489.4002 456.4128 517.0876" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M385.5257 535.3529C384.0307 535.6137 383.0304 537.0375 383.2911 538.5325 383.5519 540.0275 384.9758 541.0278 386.4708 540.7671 396.1701 539.0697 403.9597 534.7307 409.5697 528.8811 413.6706 524.6056 416.5985 519.5228 418.2517 514.0726 420.2651 507.4342 420.4681 500.6786 419.021 494.333 415.4389 478.624 400.6091 469.455 399.3046 471.1317 398.3397 472.3719 408.1983 478.1285 412.9364 492.9146 414.9176 499.0966 415.0185 505.8584 413.0093 512.483 411.6059 517.1102 409.1128 521.4331 405.6165 525.0781 400.7823 530.1187 394.0121 533.8678 385.5257 535.3529" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M347.7036 397.0247C373.7778 395.6411 372.3831 377.7194 368.7341 376.1252 365.9717 374.9185 366.9712 393.9474 342.0573 390.4237 339.6614 390.0849 339.961 392.3436 340.7706 393.6988 341.7619 395.3574 342.9527 397.2764 347.7036 397.0247" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M361.7036 413.7749C378.5647 409.3937 380.3063 401.7152 377.8594 400.5629 374.8963 399.1677 369.4711 408.009 356.0573 407.1739 353.643 407.0237 353.9611 409.0938 354.7706 410.449 355.7619 412.1076 357.0996 414.9711 361.7036 413.7749" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M378.9913 378.22C402.9318 358.3877 432.9969 354.6184 448.6876 361.6611 447.8882 354.0481 447.0661 351.125 445.7441 346.7523 419.9253 339.2286 394.6859 350.4589 376.8038 363.9702 377.8977 365.296 378.4626 366.3369 378.6454 367.662 379.1163 371.075 378.9913 375.3689 378.9913 378.22" fill="#70c9ff" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M142.9302 358.7185 143.1592 346.2525C145.8544 345.7196 148.6253 345.6711 151.1719 346.5181 150.872 350.1156 150.7238 352.9239 150.4911 355.2934 148.2678 356.5721 145.5724 356.906 142.9302 358.7185" fill="#03466c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M143.1592 346.2525 142.9302 358.7185C142.7556 361.7167 142.6663 364.739 142.6663 367.7814 142.6663 368.814 142.6765 369.8444 142.6969 370.8723H150.425L152.1125 379.3759H143.0991C143.3531 382.7656 143.7177 386.1247 144.1891 389.4491H154.4876L158.7376 397.9528H145.641C146.3207 401.3524 147.1133 404.712 148.0144 408.0263H164.7374L168.8624 416.5299H150.5962C151.7426 419.9431 153.0054 423.3025 154.381 426.6034H175.9878L184.1128 435.1071H158.2353C159.9194 438.539 161.7267 441.8989 163.6543 445.1803H208.4879L214.238 453.6839H169.0313C171.3832 457.1459 173.8732 460.5063 176.4938 463.7574 196.9087 463.7574 217.3235 463.7574 237.7383 463.7574 241.9251 465.0403 249.4925 467.024 253.8618 466.7315 262.4244 466.1578 260.4254 470.7094 265.4887 472.261H183.8806C187.1668 475.7771 190.6152 479.1404 194.2172 482.3345 225.9658 482.5151 257.7144 482.6959 289.463 482.8765 298.1268 485.8665 308.3255 488.5149 319.9893 490.8382H204.7028C209.6351 494.4926 214.7913 497.8613 220.153 500.9117H371.3207C372.871 500.0295 374.4045 499.121 375.9197 498.1867L393.5021 502.6141C366.0471 522.5556 332.2655 534.3165 295.7369 534.3165 203.7623 534.3165 129.2017 459.756 129.2017 367.7814 129.2017 362.5401 129.4438 357.3547 129.9175 352.2376 132.1336 350.6513 137.4797 347.3756 143.1592 346.2525ZM237.6051 509.4153H353.8689C335.9438 516.7806 316.3173 520.852 295.7369 520.852 275.1568 520.852 255.5306 516.7806 237.6051 509.4153" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M414.4893 370.8723C404.9901 370.8723 389.6144 379.3759 396.9893 379.3759H448.3749C448.1209 382.7656 447.7564 386.1247 447.285 389.4491H379.8017C370.6608 389.4491 379.425 391.2174 386.1173 393.7011 390.2204 395.2239 390.8931 397.9528 393.9891 397.9528H445.8331C445.1533 401.3524 444.3608 404.712 443.4596 408.0263 425.3445 408.0263 407.7133 408.0263 395.5518 408.0263 388.258 408.0263 387.9933 409.2466 386.2857 410.762 383.4848 413.2477 376.3429 416.5299 386.1145 416.5299H440.8778C439.7315 419.9431 438.4687 423.3025 437.093 426.6034 415.1835 426.6034 395.024 426.6034 373.1145 426.6034 369.201 426.6034 376.8219 432.7316 381.748 434.1937 386.2406 435.5274 391.2415 432.4901 398.8528 431.8472 412.3474 430.7074 430.6912 433.2705 439.8849 451.2348 454.121 426.6981 462.2723 398.1915 462.2723 367.7814 462.2723 363.1161 462.0804 358.4951 461.7043 353.9271 456.9744 351.0193 451.8074 348.4933 445.7441 346.7523 447.9103 354.4611 448.8077 359.8614 448.8077 367.7814 448.8077 368.814 448.7975 369.8444 448.7771 370.8723Z" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M230.2625 224.6051C201.7321 238.4203 193.3381 257.6843 168.8692 259.052 198.3866 223.9302 242.0827 201.3043 291.0464 200.1577 261.1877 221.3442 260.0169 210.1975 230.2625 224.6051" fill="#057dba" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M159.444 271.3878C169.6618 272.9044 179.2406 271.8643 188.238 268.4993 171.3334 278.4217 155.8301 283.0476 151.1969 284.5604 153.7458 280.0329 156.4991 275.6378 159.444 271.3878" fill="#03466c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M151.1969 284.5604C155.8301 283.0476 171.3334 278.4217 188.238 268.4993 191.6422 266.5012 195.103 264.2882 198.5434 261.8427 202.3693 259.1232 215.6927 246.0773 233.2089 237.8979 251.9974 229.1247 275.904 226.184 283.983 220.0581 291.3253 214.4908 302.5998 208.891 311.9641 200.984 318.0129 201.6079 323.9654 202.5607 329.8034 203.8246 317.0228 214.0135 296.2848 223.9319 287.1221 229.9099 270.0584 241.043 247.4867 242.3157 230.3929 249.7606 198.9479 263.4559 185.0465 276.8519 178.0421 280.296 174.8407 281.8704 167.7824 286.2941 150.8332 285.2093 150.954 284.9927 151.0753 284.7764 151.1969 284.5604" fill="#057dba" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M295.7332 240.8083C303.3944 233.9039 329.0046 217.0647 348.198 208.9519 354.0753 210.9682 359.8036 213.3088 365.362 215.9529 345.7559 215.7514 333.8705 225.9737 323.6224 232.6601 319.0972 235.6123 315.6905 239.1605 308.0546 244.9942Z" fill="#057dba" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M312.2439 258.3196C318.9501 252.2761 326.2031 245.6444 339.1379 238.2035 352.4162 230.565 367.9557 226.0633 380.7425 224.3024 385.933 227.4888 390.9359 230.9556 395.7299 234.682 369.2242 233.3772 353.5957 243.6103 339.7932 252.6157 333.7526 256.557 326.5169 264.1448 316.4888 270.8164Z" fill="#057dba" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M260.3543 300.1983C313.6555 307.7087 307.4647 285.8734 348.9284 262.0208 369.106 250.4134 394.1067 245.4976 408.9152 246.0883 413.263 250.2669 417.3851 254.6821 421.2612 259.3134 385.6635 254.0667 366.406 266.6806 349.638 277.6209 335.2915 286.9812 314.7211 315.3393 269.659 310.5451L265.7049 306.148Z" fill="#70c9ff" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M283.8991 322.8418C329.5755 318.852 356.508 286.2388 373.5984 278.2891 394.0132 268.7924 416.5007 268.5696 430.2808 271.1188 433.2606 275.4014 436.0456 279.8317 438.6226 284.3969 403.9342 276.3439 386.24 285.1858 369.9326 295.2643 355.3605 304.2706 324.9952 330.4321 284.2036 328.4385Z" fill="#70c9ff" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M333.1494 331.0248C351.542 326.4934 371.9769 307.6458 383.0984 302.4723 406.6744 291.5048 433.0131 294.0313 445.5147 298.0315 447.3547 302.1287 449.0351 306.3144 450.5477 310.5805 414.2503 301.5026 396.1168 309.1357 379.4326 319.4476 364.8594 328.4549 356.6829 341.9904 326.6413 337.8716 316.3178 335.1056 324.6996 333.1066 333.1494 331.0248" fill="#70c9ff" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M323.8044 347.8615C355.2134 355.6619 368.6139 337.0983 389.4486 327.4055 415.9054 315.0972 444.9886 320.3501 454.9459 325.0417L457.4307 336.4325C420.2082 326.8106 401.8272 334.5055 384.9324 344.9477 370.3589 353.9551 355.0578 361.7405 325.0162 357.6217 323.2304 357.1432 324.2083 351.1148 323.8044 347.8615" fill="#70c9ff" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M169.6581 295.1725C167.6081 299.1986 162.4895 305.7092 160.7338 309.3707 156.1757 310.2355 147.8143 309.5606 139.47 310.282 141.097 305.7214 142.9157 301.2528 144.9164 296.8863 153.3158 295.8494 161.0637 296.3378 169.6581 295.1725" fill="#03466c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M135.7387 322.1223C142.636 321.5942 148.7222 322.0727 156.2831 321.0474L153.9876 331.5708C150.1583 333.375 143.5048 332.2429 132.7524 334.9755 133.5824 330.6285 134.5802 326.3417 135.7387 322.1223" fill="#03466c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M260.3543 300.1983 265.7049 306.148C257.1435 305.0232 250.2632 307.3754 242.545 305.3075 236.5997 303.7147 237.1059 294.6132 240.7323 294.5733 247.2307 294.5018 254.4177 300.0733 260.3543 300.1983" fill="#0d669e" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M234.0476 316.1846C232.4154 319.7083 231.5546 323.6817 231.9225 328.3724 237.0906 327.3004 240.3641 324.1604 243.6727 321.0596 241.2074 318.6173 238.1803 316.7926 234.0476 316.1846" fill="#0d669e" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M330.5483 379.3759C335.09 379.3759 339.6317 379.3759 344.1733 379.3759 348.882 379.3759 353.2153 378.8756 358.2989 377.1882 358.2989 374.3535 358.2989 373.7069 358.2989 370.8723 344.9656 370.8723 331.632 370.8723 318.2987 370.8723 313.9914 371.2229 315.1259 374.9023 323.2936 376.5095 326.4015 377.1213 327.4682 379.3759 330.5483 379.3759" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M271.4644 449.1198C283.614 461.3465 302.4575 462.9824 324.1911 464.6443 326.6609 467.3633 330.2246 469.0704 334.1871 469.0704 337.3163 469.0704 340.1966 468.006 342.4859 466.2193 350.299 467.0544 358.3256 468.2251 366.4174 470.1461 378.0505 482.0913 390.1731 481.4495 360.0266 477.3376 326.31 472.7387 285.3941 473.269 269.4566 457.3315 261.5434 449.4183 256.4969 438.5871 256.4969 426.5124 256.4969 420.0877 257.9564 413.9912 260.5408 408.5133 259.8236 415.6946 259.9784 421.9631 260.8872 427.4413 261.3112 429.9985 261.8997 432.3832 262.6404 434.6084 264.5949 440.4798 267.6095 445.2403 271.4644 449.1198" fill="#ffed00" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M271.4644 449.1198C283.614 461.3465 302.4575 462.9824 324.1911 464.6443 326.6609 467.3633 330.2246 469.0704 334.1871 469.0704 337.3163 469.0704 340.1966 468.006 342.4859 466.2193 350.299 467.0544 358.3256 468.2251 366.4174 470.1461 378.0505 482.0913 390.1731 481.4495 360.0266 477.3376 326.31 472.7387 285.3941 473.269 269.4566 457.3315 261.5434 449.4183 256.4969 438.5871 256.4969 426.5124 256.4969 420.0877 257.9564 413.9912 260.5408 408.5133 259.8236 415.6946 259.9784 421.9631 260.8872 427.4413 261.3112 429.9985 261.8997 432.3832 262.6404 434.6084 264.5949 440.4798 267.6095 445.2403 271.4644 449.1198" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M332.6706 465.7813C335.2689 465.7813 337.375 463.6752 337.375 461.0772 337.375 458.479 335.2689 456.3729 332.6706 456.3729 330.0727 456.3729 327.9665 458.479 327.9665 461.0772 327.9665 463.6752 330.0727 465.7813 332.6706 465.7813" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M243.1862 342.6276C260.8693 332.9674 260.3993 316.7325 256.542 316.1795 253.5576 315.7517 255.187 332.0558 237.8812 338.0142 235.5928 338.8022 236.0058 340.1507 236.9585 341.4098 238.1247 342.9516 239.0102 344.9086 243.1862 342.6276" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M257.3283 353.5869C278.8404 345.8906 276.8831 328.7194 273.2338 327.1249 270.4711 325.9179 272.9083 344.7592 252.557 348.4233 250.1753 348.8519 250.4602 350.3432 251.2704 351.6985 252.2616 353.3579 252.8484 355.1896 257.3283 353.5869" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M271.7031 364.212C295.5277 360.0159 294.3638 343.2404 290.6708 341.7503 286.5957 340.1062 290.283 357.3221 267.5568 358.5484 265.1406 358.6785 264.8353 360.9683 265.6451 362.3236 266.6364 363.9827 267.0174 365.0372 271.7031 364.212" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M285.8281 374.7121C315.9028 374.0162 317.2391 348.4902 313.5461 347.0003 309.4713 345.3562 311.5332 370.9471 281.6819 369.0485 279.2673 368.8948 278.9603 371.4684 279.7702 372.8237 280.7614 374.4828 281.0716 374.8221 285.8281 374.7121" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M244.4352 451.9349C254.9242 452.3545 251.2244 452.3094 248.3391 441.8819 241.975 418.8821 256.911 401.002 261.214 386.6114 265.5816 372.003 251.9461 356.8289 250.4174 380.4049 249.7479 390.731 226.0225 411.122 223.2473 414.9349 206.8744 437.4292 223.45 451.0959 244.4352 451.9349" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M198.3844 429.5477C207.614 434.5483 205.6881 432.6891 207.6951 422.058 210.2281 408.6403 230.4649 397.7425 238.8682 383.1868 248.6653 366.2172 240.3774 345.8594 229.4133 366.9812 225.2259 375.048 206.7103 385.4778 198.1783 389.3717 173.3162 400.7188 179.9184 419.5434 198.3844 429.5477" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M169.8242 393.1217C171.36 396.3436 173.6073 398.4985 175.2401 398.0611 177.0364 397.58 178.3259 391.8427 183.7803 387.8084 194.7572 379.6883 211.5828 374.0318 222.4604 361.22 228.4923 354.1153 229.4271 344.37 227.0378 341.2193 224.9084 338.4116 222.1243 341.6343 211.8648 346.1607 203.5539 349.8276 190.2515 355.0357 181.0607 356.8997 155.6782 362.0477 158.9357 370.2835 169.8242 393.1217" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M161.765 346.9022C161.4254 349.6966 162.0105 352.0817 163.2824 352.4805 164.6819 352.9199 168.0695 350.3752 173.3037 349.2652 185.0088 346.7829 196.5152 342.0978 209.1739 338.167 216.1939 335.9874 220.7404 331.4166 220.4821 328.3087 220.252 325.5395 213.205 324.7393 204.3618 324.6605 165.5056 324.3132 164.8383 321.6044 161.765 346.9022" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M172.8232 309.6541C171.6429 312.1447 171.4482 314.5414 172.506 315.3056 173.0268 315.6817 207.1704 316.1469 210.5079 316.1599 227.2201 316.2249 228.0404 299.3715 225.1491 299.4854 201.4498 300.4172 200.031 285.1693 195.8434 284.5797 192.8055 284.152 179.1694 296.2616 172.8232 309.6541" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M206.4317 273.5796C204.1512 275.1267 202.0513 276.0055 203.2909 278.2885 207.7064 286.4217 221.0803 294.0508 228.7128 290.4341 235.4477 287.2429 238.7146 284.5797 239.1687 279.6287 239.6378 274.5133 236.9962 269.458 233.0963 265.5601 227.4639 259.9311 216.301 266.8842 206.4317 273.5796" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M251.1247 278.4969C267.0469 278.4969 282.9688 278.4969 298.891 278.4969 302.798 278.4969 303.2915 278.3384 301.4113 281.5948 294.8763 292.9133 280.2591 291.5461 267.0131 290.1249 256.5275 288.9998 245.9177 287.8228 246.75 284.747 247.1491 283.2707 247.5672 281.7992 247.9839 280.3275 248.5644 278.2769 248.4283 278.4969 251.1247 278.4969" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M249.6246 270.1789C265.5468 270.1789 287.0938 270.1789 303.016 270.1789 306.923 270.1789 307.4162 270.337 305.5363 267.0809 299.0015 255.7627 288.6032 244.9553 268.8883 249.5509 240.2328 256.2304 238.5547 260.7588 241.4373 262.1163 244.836 263.7173 246.0674 266.8765 246.4838 268.3483 247.0643 270.3986 246.9283 270.1789 249.6246 270.1789" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M347.6871 455.5704C347.6871 448.1144 341.6431 442.0704 334.1871 442.0704 329.2047 442.0704 324.8527 444.7695 322.5135 448.7854 324.6625 443.8933 329.557 440.4759 335.2516 440.4759 342.9295 440.4759 349.1535 446.6886 349.1535 454.352 349.1535 459.3342 346.5232 463.7032 342.5732 466.1507 345.689 463.6777 347.6871 459.8575 347.6871 455.5704" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M347.6871 455.5704C347.6871 448.1144 341.6431 442.0704 334.1871 442.0704 329.2047 442.0704 324.8527 444.7695 322.5135 448.7854 324.6625 443.8933 329.557 440.4759 335.2516 440.4759 342.9295 440.4759 349.1535 446.6886 349.1535 454.352 349.1535 459.3342 346.5232 463.7032 342.5732 466.1507 345.689 463.6777 347.6871 459.8575 347.6871 455.5704" fill="#ed6f1c" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M361.0035 438.4196C340.1218 414.527 272.5744 390.3123 262.6404 434.6084 261.8997 432.3832 261.3112 429.9982 260.8872 427.4413 271.1347 387.6474 322.6017 399.024 352.8142 419.7158 355.0258 423.5769 356.7084 427.4073 357.7473 431.0124 358.4886 433.5849 359.6034 436.0397 361.0035 438.4196" fill="#ffed00" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M361.0035 438.4196C340.1218 414.527 272.5744 390.3123 262.6404 434.6084 261.8997 432.3832 261.3112 429.9982 260.8872 427.4413 271.1347 387.6474 322.6017 399.024 352.8142 419.7158 355.0258 423.5769 356.7084 427.4073 357.7473 431.0124 358.4886 433.5849 359.6034 436.0397 361.0035 438.4196" fill="#d14a00" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M352.8142 419.7158C322.6017 399.024 271.1347 387.6474 260.8872 427.4413 259.9784 421.9628 259.8236 415.6946 260.5408 408.5133 267.537 393.6835 282.7772 383.3872 299.622 383.3872 321.0939 383.3872 342.5978 401.8802 352.8142 419.7158" fill="#ffed00" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M352.8142 419.7158C322.6017 399.024 271.1347 387.6474 260.8872 427.4413 259.9784 421.9628 259.8236 415.6946 260.5408 408.5133 267.537 393.6835 282.7772 383.3872 299.622 383.3872 321.0939 383.3872 342.5978 401.8802 352.8142 419.7158" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M267.042 290.0724C256.5564 288.9473 245.9466 287.7704 246.7789 284.6945 247.178 283.2182 247.5961 281.7468 248.0128 280.275 248.5933 278.2244 248.4573 278.4444 251.1536 278.4444H298.9199C302.8269 278.4444 303.3204 278.286 301.4402 281.5424 294.9052 292.8609 280.2881 291.4937 267.042 290.0724ZM259.7414 281.0673C257.3504 281.0812 263.0693 283.6168 270.6613 284.2515 280.9851 285.1143 293.9899 285.9557 294.1316 280.8655Z" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M299.7848 263.3924C286.7272 250.9648 264.0866 258.6535 245.9551 266.7643 245.2538 265.2026 243.9292 263.2241 241.4662 262.0638 238.5836 260.7063 240.2617 256.178 268.9172 249.4984 286.4823 245.4041 296.6519 253.5364 303.2824 263.3632 302.2396 263.3443 301.0652 263.3547 299.7848 263.3924" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M255.2666 270.1264C265.0195 266.6874 287.3857 263.7584 299.7848 263.3924 301.0652 263.3547 302.2396 263.3443 303.2824 263.3632 304.094 264.566 304.8525 265.7942 305.5652 267.0284 307.4451 270.2846 306.9519 270.1264 303.0449 270.1264Z" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M233.1252 265.5077C235.6228 268.0041 237.6045 270.9751 238.5833 274.1326 225.8036 269.7851 213.9704 278.5022 206.6888 282.7259 205.3114 281.2958 204.1551 279.775 203.3198 278.2361 202.0802 275.953 204.1801 275.0743 206.4606 273.5271 216.33 266.8317 227.4928 259.8786 233.1252 265.5077" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M217.9505 290.0784C223.9206 284.0567 232.454 280.2538 239.2475 278.0725 239.2597 278.5725 239.2435 279.074 239.1976 279.5763 238.7435 284.5273 235.4766 287.1904 228.7417 290.3817 225.7359 291.8061 221.8394 291.486 217.9505 290.0784" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M195.8723 284.5273C197.708 284.7858 199.012 287.8614 201.894 291.1561 190.51 295.8225 184.0674 306.949 179.4521 315.5777 175.4561 315.4706 172.6807 315.3583 172.535 315.2531 171.4771 314.4889 171.6718 312.0922 172.8522 309.6017 179.1984 296.2094 192.8344 284.0998 195.8723 284.5273" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M187.213 315.7543C201.562 312.0171 211.0317 313.6595 216.6727 308.1881 224.9592 300.1504 212.3833 303.2342 212.9162 298.1514 216.1684 299.1059 220.1771 299.6297 225.178 299.433 228.0691 299.319 227.249 316.1724 210.5368 316.1075 208.6645 316.1001 197.0997 315.9507 187.213 315.7543" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M161.7939 346.8498C164.8672 321.552 165.5345 324.2608 204.3907 324.608 210.9623 324.6667 216.5417 325.1237 219.0694 326.4908 205.6782 327.7006 182.2952 329.4128 177.1795 332.4523 173.5838 334.5888 169.2017 348.556 165.6961 351.9051 164.63 352.3414 163.8326 352.5917 163.3113 352.4281 162.0394 352.0293 161.4543 349.6442 161.7939 346.8498" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M176.3887 348.5157C184.7954 343.1432 193.1666 341.4376 201.4929 340.6212 192.8477 343.5284 184.6383 346.5074 176.3887 348.5157" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M181.0896 356.8473C190.2804 354.9832 203.5828 349.7751 211.8937 346.1083 222.1532 341.5819 224.9374 338.3592 227.0667 341.1669 228.498 343.0542 228.7363 347.3082 227.4009 351.9431 226.2283 351.059 224.5317 350.7917 222.393 350.9473 212.6497 351.6571 193.7339 361.1489 173.4182 361.095 173.8947 359.9751 173.7218 359.2967 172.186 359.1771 174.6485 358.3222 177.5979 357.5554 181.0896 356.8473" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M168.8088 375.8783C169.8146 379.4771 171.0887 383.4054 174.6125 385.6592 178.8299 388.3569 185.9967 379.8431 193.5836 374.9462 200.6087 370.4125 207.599 367.3826 210.4705 369.2529 211.0156 369.608 211.4121 370.1395 211.6324 370.8709 202.2086 377.5249 191.5534 382.0272 183.8092 387.756 178.3548 391.7903 177.0653 397.5276 175.269 398.0086 173.6362 398.446 171.3889 396.2911 169.8531 393.0693 160.4625 373.3727 156.7477 364.5374 172.186 359.1771 173.7218 359.2967 173.8947 359.9751 173.4182 361.095 172.23 363.8871 167.0049 369.4229 168.8088 375.8783" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M210.4705 369.2529C217.5942 362.2799 223.1147 355.7506 222.393 350.9473 224.5317 350.7917 226.2283 351.059 227.4009 351.9431 226.5069 355.0456 224.9079 358.3188 222.4894 361.1676 219.2859 364.9408 215.5663 368.0932 211.6324 370.8709 211.4121 370.1395 211.0156 369.608 210.4705 369.2529" fill="#d14a00" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M191.7884 392.8538C194.9258 392.5851 195.1897 393.5568 194.3408 395.3571 192.7885 398.6484 187.5161 404.7089 189.2806 411.0222 190.2866 414.6214 194.4295 418.5558 197.9538 420.8105 204.2345 424.8283 203.7997 421.3227 207.4632 416.1835 219.3194 399.5552 226.2532 393.4517 230.1806 391.8614 231.2232 391.4393 232.0537 391.3353 232.7085 391.4368 223.1419 402.1262 209.753 411.2575 207.724 422.0056 205.717 432.6367 207.6429 434.4959 198.4133 429.4953 181.7561 420.4709 174.7525 404.2698 191.7884 392.8538" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M198.2072 389.3193C206.7392 385.4254 225.2548 374.9956 229.4422 366.9287 240.4063 345.8069 248.6942 366.1648 238.8971 383.1344 237.2253 386.0306 235.0848 388.7816 232.7085 391.4368 232.0537 391.3353 231.2232 391.4393 230.1806 391.8614 230.3022 388.0123 238.9504 372.7009 235.8984 373.2452 228.3375 374.5933 217.6101 386.2366 194.3408 395.3571 195.1897 393.5568 194.9258 392.5851 191.7884 392.8538 193.6383 391.614 195.772 390.4308 198.2072 389.3193" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M244.4641 451.8825C223.6569 451.0505 207.1848 437.6075 222.8669 415.4528 224.5255 415.0962 224.8773 416.2402 224.7741 418.2548 224.5309 423.0048 221.7583 432.595 227.6206 438.7674 232.5192 443.9251 244.7113 447.5869 250.9019 448.9894 252.1667 452.238 251.5864 452.1674 244.4641 451.8825" fill="#fff7d4" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M259.5747 391.2041C247.3639 401.2421 236.4171 407.7904 224.7741 418.2548 224.8773 416.2402 224.5255 415.0962 222.8669 415.4528 223.001 415.2631 223.1374 415.0732 223.2763 414.8824 226.0514 411.0695 249.7768 390.6785 250.4463 380.3525 251.9751 356.7764 265.6106 371.9506 261.2429 386.5589 260.7917 388.0684 260.2233 389.6161 259.5747 391.2041" fill="#a83b03" fill-rule="evenodd"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M109.1186 131.8674V132.0352C109.1186 136.2353 108.3487 140.1693 106.8086 143.8373 105.2685 147.5053 103.1128 150.6974 100.3408 153.4133 97.5688 156.1292 94.2786 158.2852 90.4706 159.8814 86.6628 161.4773 82.4907 162.2752 77.9547 162.2752 73.4187 162.2752 69.2328 161.4773 65.3967 159.8814 61.5609 158.2852 58.2568 156.1014 55.4848 153.3294 52.7128 150.5574 50.5429 147.3372 48.9747 143.6692 47.4066 140.0012 46.6228 136.0672 46.6228 131.8674V131.6993C46.6228 127.4992 47.3927 123.5653 48.9328 119.8973 50.4729 116.2293 52.6286 113.0374 55.4006 110.3213 58.1726 107.6054 61.4628 105.4494 65.2708 103.8532 69.0786 102.2573 73.2506 101.4593 77.7866 101.4593 82.3226 101.4593 86.5086 102.2573 90.3447 103.8532 94.1808 105.4494 97.4846 107.6332 100.2566 110.4052 103.0286 113.1772 105.1988 116.3974 106.7666 120.0654 108.3348 123.7334 109.1186 127.6673 109.1186 131.8674ZM95.5947 131.6993C95.5947 129.1793 95.1608 126.8133 94.2928 124.6014 93.4248 122.3892 92.2207 120.4432 90.6806 118.7634 89.1408 117.0833 87.2787 115.7672 85.0946 114.8153 82.9108 113.8635 80.5306 113.3872 77.9547 113.3872 75.3228 113.3872 72.9286 113.8774 70.7726 114.8573 68.6169 115.8372 66.7406 117.1672 65.1447 118.8473 63.5488 120.5274 62.3169 122.4873 61.4486 124.7272 60.5806 126.9672 60.1466 129.3474 60.1466 131.8674V132.0352C60.1466 134.5552 60.5806 136.9213 61.4486 139.1332 62.3169 141.3454 63.5207 143.2913 65.0608 144.9714 66.6009 146.6512 68.4488 147.9674 70.6048 148.9192 72.7608 149.8714 75.1547 150.3473 77.7866 150.3473 80.3628 150.3473 82.7427 149.8572 84.9268 148.8773 87.1106 147.8974 89.0008 146.5673 90.5967 144.8872 92.1926 143.2074 93.4248 141.2473 94.2928 139.0073 95.1608 136.7674 95.5947 134.3874 95.5947 131.8674ZM172.1733 102.4673 157.8093 123.4672C161.5615 124.8673 164.5994 127.0653 166.9235 130.0612 169.2473 133.0574 170.4093 136.9352 170.4093 141.6954V141.8632C170.4093 147.9674 168.4633 152.7273 164.5713 156.1433 160.6793 159.5594 155.2334 161.2672 148.2335 161.2672H121.3534V102.4673H134.2894V121.2834H144.4535L157.0535 102.4673ZM157.3055 141.0233C157.3055 138.5033 156.4515 136.4873 154.7433 134.9753 153.0354 133.4633 150.5854 132.7073 147.3934 132.7073H134.2894V149.5913H147.1414C150.3334 149.5913 152.8254 148.8912 154.6174 147.4911 156.4095 146.0914 157.3055 143.9912 157.3055 141.1914ZM240.1002 102.4673 214.9002 161.6873H202.9723L177.7723 102.4673H190.9602L196.3363 115.6552H221.2002L226.5761 102.4673ZM216.5803 127.0794H200.9563L208.7683 146.1472ZM302.1469 102.4673V161.2672H289.3788V125.0634L261.8269 161.2672H249.899V102.4673H262.6671V139.8472L291.1428 102.4673ZM370.4097 110.7833V136.3192H344.8738V125.1473H357.8936V116.6913C354.5895 114.3252 350.5297 113.1352 345.7137 113.1352 343.0817 113.1352 340.6876 113.5973 338.5315 114.5214 336.3758 115.4452 334.4995 116.7613 332.9036 118.4414 331.3077 120.1212 330.0758 122.0953 329.2075 124.3774 328.3396 126.6454 327.9056 129.1374 327.9056 131.8252V131.9933C327.9056 134.5133 328.3396 136.8933 329.2075 139.1054 330.0758 141.3312 331.2797 143.2633 332.8197 144.9153 334.3598 146.5673 336.1516 147.8974 338.1956 148.8773 340.2397 149.8572 342.4377 150.3473 344.7896 150.3473 346.4697 150.3473 348.0097 150.2073 349.4098 149.9272 350.8095 149.6472 352.1117 149.2551 353.3156 148.7511 354.5195 148.2471 355.6817 147.6312 356.8017 146.9032 357.9216 146.1753 359.0416 145.3354 360.1616 144.3832L368.3095 154.2112C366.7975 155.4993 365.2296 156.6473 363.6057 157.6553 361.9817 158.6633 360.2455 159.5032 358.3976 160.1753 356.5497 160.8471 354.5476 161.3653 352.3915 161.7293 350.2355 162.0933 347.8416 162.2752 345.2097 162.2752 340.7295 162.2752 336.6136 161.4773 332.8617 159.8814 329.1097 158.2852 325.8615 156.1014 323.1176 153.3294 320.3736 150.5574 318.2318 147.3372 316.6917 143.6692 315.1517 140.0012 314.3818 136.0672 314.3818 131.8674V131.6993C314.3818 127.3314 315.1378 123.2994 316.6498 119.6033 318.1618 115.9072 320.2756 112.7154 322.9917 110.0273 325.7076 107.3392 328.9555 105.2393 332.7355 103.7273 336.5155 102.2153 340.7017 101.4593 345.2936 101.4593 350.6698 101.4593 355.4436 102.3693 359.6156 104.1894 363.7877 106.0092 367.3857 108.2072 370.4097 110.7833ZM428.2563 102.4673V113.9754H396.3365V126.3234H424.0562V137.8312H396.3365V149.7591H427.8362V161.2672H383.5684V102.4673ZM492.6549 161.2672H477.955L463.6752 137.6634 449.6471 161.2672H434.5271L457.1232 125.6513V102.4673H470.0591V125.9033ZM558.7336 102.4673 533.5336 161.6873H521.6057L496.4057 102.4673H509.5936L514.9698 115.6552H539.8336L545.2095 102.4673ZM535.2137 127.0794H519.5897L527.4017 146.1472Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" d="M269.3631 54.4674V60.1679H255.0506V83.1747H248.7351V54.4674ZM306.9068 54.4674V60.1679H292.5944V83.1747H286.2788V54.4674ZM346.5422 73.1682V73.2501C346.5422 74.7264 346.2891 76.0731 345.7834 77.2897 345.2777 78.5064 344.5532 79.5521 343.6098 80.4268 342.6667 81.3019 341.5184 81.9785 340.1651 82.457 338.8116 82.9355 337.2738 83.1747 335.5515 83.1747H323.8226V54.4674H330.1381V63.0797H334.9363C336.5493 63.0797 338.0598 63.2914 339.4678 63.7152 340.8758 64.139 342.1063 64.7748 343.1588 65.6223 344.2113 66.4699 345.0385 67.5224 345.64 68.7801 346.2415 70.0376 346.5422 71.5003 346.5422 73.1682ZM340.1444 73.0451C340.1444 71.8149 339.7071 70.7828 338.8323 69.9489 337.9572 69.1149 336.727 68.6979 335.1413 68.6979H330.1381V77.4743H335.0183C336.604 77.4743 337.8549 77.1052 338.7708 76.3668 339.6866 75.6286 340.1444 74.5489 340.1444 73.1271Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" stroke-width="7.9999" stroke-linecap="butt" stroke-miterlimit="22.925585" stroke-linejoin="miter" fill="none" stroke="#f5782e" d="M194.7787 69.4256H222.7788"/>
<path transform="matrix(1,0,0,-1,0,595.2756)" stroke-width="7.9999" stroke-linecap="butt" stroke-miterlimit="22.925585" stroke-linejoin="miter" fill="none" stroke="#f5782e" d="M400.4986 69.4256H372.4985"/>
</g>
</svg>
