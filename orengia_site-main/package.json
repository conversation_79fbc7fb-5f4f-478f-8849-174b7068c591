{"name": "org", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js", "server:dev": "nodemon server.js", "start:all": "concurrently \"npm run server:dev\" \"npm run dev\""}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "cors": "^2.8.5", "express": "^4.18.2", "multer": "^2.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.0.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}}