# ECMPS - East Coast Marine Products and Supplies

A modern, responsive website for East Coast Marine Products and Supplies, built with React, Vite, Tailwind CSS, and Express.js API server.

## Features

- **Modern Design**: Clean, professional design with responsive layout
- **Product Catalog**: Comprehensive product listing with categories and detailed views
- **Company Information**: About us page with company history and positioning
- **Contact Form**: Interactive contact form for customer inquiries
- **API Server**: Express.js backend with JSON database
- **Real-time Data**: Live API integration with search and filtering
- **Mobile Responsive**: Optimized for all device sizes

## Tech Stack

### Frontend
- **React 19**: Modern React with hooks
- **Vite**: Fast build tool and development server
- **Tailwind CSS 4**: Utility-first CSS framework
- **React Router**: Client-side routing
- **Montserrat Font**: Professional typography

### Backend
- **Express.js**: RESTful API server
- **CORS**: Cross-origin resource sharing
- **JSON Database**: File-based data storage
- **ES Modules**: Modern JavaScript modules

## Pages

1. **Home**: Hero section, company overview, featured products
2. **About Us**: Company history, journey, and positioning
3. **Products**: Product catalog with category filtering
4. **Product Detail**: Individual product specifications and details
5. **Contact Us**: Contact form and company information

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application

#### Option 1: Run both frontend and backend together (Recommended)

```bash
npm run start:all
```

This will start:
- Express API server on `http://localhost:3001`
- React development server on `http://localhost:5173`

#### Option 2: Run separately

**Start the API server:**
```bash
npm run server:dev
```

**Start the React app (in another terminal):**
```bash
npm run dev
```

### Available Scripts

- `npm run dev` - Start React development server
- `npm run build` - Build React app for production
- `npm run server` - Start Express server (production)
- `npm run server:dev` - Start Express server with nodemon (development)
- `npm run start:all` - Start both frontend and backend concurrently
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

### Build for Production

```bash
npm run build
```

## API Endpoints

### Products
- `GET /api/products` - Get all products with filtering and pagination
- `GET /api/products/:id` - Get single product by ID or slug
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get single category by ID or slug

### Forms
- `POST /api/contact` - Submit contact form
- `POST /api/quote` - Submit quote request

### Query Parameters for Products

- `category` - Filter by category slug
- `featured` - Filter featured products (true/false)
- `search` - Search in name, description, and tags
- `limit` - Limit number of results
- `offset` - Pagination offset
- `sortBy` - Sort field (default: createdAt)
- `sortOrder` - Sort order (asc/desc, default: desc)

### Example API Calls

```bash
# Get all products
curl http://localhost:3001/api/products

# Get featured products
curl http://localhost:3001/api/products?featured=true&limit=4

# Search products
curl http://localhost:3001/api/products?search=artemia

# Get products by category
curl http://localhost:3001/api/products?category=artemia-cysts
```

## Admin Panel

The application includes a comprehensive admin panel for managing products, categories, and featured items.

### Admin Access

- **URL**: `http://localhost:5173/admin/login`
- **Default Credentials**:
  - Username: `admin`
  - Password: `admin123`

### Admin Features

#### 🏠 Dashboard
- Overview statistics (total products, featured items, categories)
- Quick action buttons
- Recent activity log

#### 📦 Product Management
- **View Products**: List all products with search and filtering
- **Add Product**: Create new products with full details
- **Edit Product**: Update existing product information
- **Delete Product**: Remove products from catalog
- **Toggle Featured**: Mark/unmark products as featured
- **Image Management**: Upload and manage product images (main, thumbnail, cover, gallery)

#### 📂 Category Management
- **View Categories**: List all product categories
- **Add Category**: Create new categories
- **Edit Category**: Update category information
- **Delete Category**: Remove categories
- **Toggle Featured**: Mark categories for homepage display
- **Image Management**: Upload category thumbnails and cover images

#### ⭐ Featured Items Management
- **Featured Products**: Manage which products appear in featured sections
- **Featured Categories**: Control which categories appear in homepage hero section
- **Visual Management**: Easy toggle interface for featured status

### Admin API Endpoints

#### Product Management
- `POST /api/admin/products` - Create new product
- `PUT /api/admin/products/:id` - Update product
- `DELETE /api/admin/products/:id` - Delete product
- `PATCH /api/admin/products/:id/featured` - Toggle featured status

#### Category Management
- `POST /api/admin/categories` - Create new category
- `PUT /api/admin/categories/:id` - Update category
- `DELETE /api/admin/categories/:id` - Delete category
- `PATCH /api/admin/categories/:id/featured` - Toggle featured status

### Image Management

The admin panel supports image management for:

#### Products
- **Main Image**: Primary product display image
- **Thumbnail**: Small preview image for listings
- **Cover Image**: Large banner image for product pages
- **Gallery**: Multiple images for product showcase

#### Categories
- **Thumbnail**: Small image for category listings
- **Cover Image**: Large banner for category pages
- **Featured Display**: Images shown in homepage hero section

### Data Structure Updates

#### Enhanced Product Schema
```json
{
  "id": 1,
  "name": "Product Name",
  "image": "/src/assets/images/products/main.jpg",
  "thumbnail": "/src/assets/images/products/thumb.jpg",
  "coverImage": "/src/assets/images/products/cover.jpg",
  "gallery": ["image1.jpg", "image2.jpg"],
  "featured": true,
  // ... other fields
}
```

#### Enhanced Category Schema
```json
{
  "id": 1,
  "name": "Category Name",
  "thumbnail": "/src/assets/images/categories/thumb.jpg",
  "coverImage": "/src/assets/images/categories/cover.jpg",
  "featured": true,
  // ... other fields
}
```

### Homepage Integration

- **Hero Section**: Now displays featured categories instead of static content
- **Dynamic Content**: Categories marked as "featured" automatically appear
- **Interactive**: Category cards link to filtered product listings
- **Responsive**: Optimized for all device sizes

## Asset Requirements

The following assets need to be added to complete the design:

### Images Needed:
- Hero background image (fish/aquaculture theme)
- Company photos for About Us section
- Product images for each category
- Team/facility photos

### Icons Needed:
- Search icon
- Contact icons (phone, email, location)
- Social media icons

### Logos Needed:
- Company logo (main)
- Partner brand logos

## Customization

### Colors
The color scheme can be customized in `tailwind.config.js`:
- Primary: Blue tones
- Accent: Orange (#f97316)
- Navy: Dark blue backgrounds

### Typography
Montserrat font is loaded from Google Fonts and set as the default sans-serif font.

### Content
Product data can be updated in `src/data/products.js`.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is proprietary to East Coast Marine Products and Supplies.
