import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import multer from 'multer';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if file is an image
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve uploaded images as static files
app.use('/uploads', express.static(uploadsDir));

// Load JSON database
const loadDatabase = () => {
  try {
    const dataPath = path.join(__dirname, 'src', 'data', 'products.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    return JSON.parse(rawData);
  } catch (error) {
    console.error('Error loading database:', error);
    return { products: [], categories: [] };
  }
};

// Save database (for future write operations)
const saveDatabase = (data) => {
  try {
    const dataPath = path.join(__dirname, 'src', 'data', 'products.json');
    fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving database:', error);
    return false;
  }
};

// Helper function to create API response
const createResponse = (data, meta = {}) => ({
  success: true,
  data,
  meta: {
    timestamp: new Date().toISOString(),
    ...meta
  }
});

// Helper function to create error response
const createErrorResponse = (message, statusCode = 500) => ({
  success: false,
  error: {
    message,
    statusCode,
    timestamp: new Date().toISOString()
  }
});

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json(createResponse({ status: 'OK', server: 'Orangeya API Server' }));
});

// Image upload endpoint
app.post('/api/upload', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json(createResponse(
        null, 
        'No file uploaded', 
        400
      ));
    }

    const fileUrl = `/uploads/${req.file.filename}`;
    
    res.json(createResponse({
      message: 'File uploaded successfully',
      filename: req.file.filename,
      originalName: req.file.originalname,
      url: fileUrl,
      size: req.file.size
    }));
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json(createResponse(
      null, 
      'File upload failed', 
      500
    ));
  }
});

// Get all products with filtering and pagination
app.get('/api/products', (req, res) => {
  try {
    const db = loadDatabase();
    const {
      category,
      featured,
      limit,
      offset = 0,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let products = [...db.products];

    // Filter by category
    if (category && category !== 'all') {
      products = products.filter(product =>
        product.categorySlug === category ||
        product.category.toLowerCase() === category.toLowerCase()
      );
    }

    // Filter by featured
    if (featured === 'true') {
      products = products.filter(product => product.featured);
    }

    // Search filter
    if (search) {
      const searchLower = search.toLowerCase();
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        product.longDescription.toLowerCase().includes(searchLower) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
        product.brand.toLowerCase().includes(searchLower)
      );
    }

    // Sorting
    products.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // Pagination
    const total = products.length;
    const limitNum = limit ? parseInt(limit) : total;
    const offsetNum = parseInt(offset);

    if (limit) {
      products = products.slice(offsetNum, offsetNum + limitNum);
    }

    const meta = {
      total,
      limit: limitNum,
      offset: offsetNum,
      hasMore: limitNum ? (offsetNum + limitNum) < total : false,
      page: Math.floor(offsetNum / limitNum) + 1,
      totalPages: Math.ceil(total / limitNum)
    };

    res.json(createResponse(products, meta));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Get single product by ID or slug
app.get('/api/products/:identifier', (req, res) => {
  try {
    const db = loadDatabase();
    const { identifier } = req.params;

    const product = db.products.find(p =>
      p.id === parseInt(identifier) || p.slug === identifier
    );

    if (!product) {
      return res.status(404).json(createErrorResponse('Product not found', 404));
    }

    res.json(createResponse(product));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Get all categories
app.get('/api/categories', (req, res) => {
  try {
    const db = loadDatabase();
    const meta = {
      total: db.categories.length
    };

    res.json(createResponse(db.categories, meta));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Get single category by ID or slug
app.get('/api/categories/:identifier', (req, res) => {
  try {
    const db = loadDatabase();
    const { identifier } = req.params;

    const category = db.categories.find(c =>
      c.id === parseInt(identifier) || c.slug === identifier
    );

    if (!category) {
      return res.status(404).json(createErrorResponse('Category not found', 404));
    }

    res.json(createResponse(category));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Contact form submission
app.post('/api/contact', (req, res) => {
  try {
    const { name, email, phone, company, message } = req.body;

    // Basic validation
    if (!name || !email || !message) {
      return res.status(400).json(createErrorResponse('Name, email, and message are required', 400));
    }

    // Simulate saving to database or sending email
    const contactSubmission = {
      id: Date.now(),
      name,
      email,
      phone,
      company,
      message,
      submittedAt: new Date().toISOString(),
      status: 'pending'
    };

    console.log('Contact form submitted:', contactSubmission);

    res.json(createResponse({
      id: contactSubmission.id,
      message: 'Thank you for your message! We will get back to you soon.',
      submittedAt: contactSubmission.submittedAt
    }));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Quote request submission
app.post('/api/quote', (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      company,
      products,
      quantities,
      message
    } = req.body;

    // Basic validation
    if (!name || !email || !products || !quantities) {
      return res.status(400).json(createErrorResponse('Name, email, products, and quantities are required', 400));
    }

    // Simulate saving to database
    const quoteRequest = {
      id: `QT-${Date.now()}`,
      name,
      email,
      phone,
      company,
      products,
      quantities,
      message,
      submittedAt: new Date().toISOString(),
      status: 'pending'
    };

    console.log('Quote request submitted:', quoteRequest);

    res.json(createResponse({
      quoteId: quoteRequest.id,
      message: 'Quote request submitted successfully! We will contact you within 24 hours.',
      submittedAt: quoteRequest.submittedAt
    }));
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Admin API Routes

// Create new product
app.post('/api/admin/products', (req, res) => {
  try {
    const db = loadDatabase();
    const newProduct = {
      ...req.body,
      id: Math.max(...db.products.map(p => p.id)) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    db.products.push(newProduct);

    if (saveDatabase(db)) {
      res.status(201).json(createResponse(newProduct));
    } else {
      res.status(500).json(createErrorResponse('Failed to save product'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Update product
app.put('/api/admin/products/:id', (req, res) => {
  try {
    const db = loadDatabase();
    const productId = parseInt(req.params.id);
    const productIndex = db.products.findIndex(p => p.id === productId);

    if (productIndex === -1) {
      return res.status(404).json(createErrorResponse('Product not found', 404));
    }

    db.products[productIndex] = {
      ...db.products[productIndex],
      ...req.body,
      id: productId,
      updatedAt: new Date().toISOString()
    };

    if (saveDatabase(db)) {
      res.json(createResponse(db.products[productIndex]));
    } else {
      res.status(500).json(createErrorResponse('Failed to update product'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Delete product
app.delete('/api/admin/products/:id', (req, res) => {
  try {
    const db = loadDatabase();
    const productId = parseInt(req.params.id);
    const productIndex = db.products.findIndex(p => p.id === productId);

    if (productIndex === -1) {
      return res.status(404).json(createErrorResponse('Product not found', 404));
    }

    const deletedProduct = db.products.splice(productIndex, 1)[0];

    if (saveDatabase(db)) {
      res.json(createResponse({ message: 'Product deleted successfully', product: deletedProduct }));
    } else {
      res.status(500).json(createErrorResponse('Failed to delete product'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Toggle product featured status
app.patch('/api/admin/products/:id/featured', (req, res) => {
  try {
    const db = loadDatabase();
    const productId = parseInt(req.params.id);
    const productIndex = db.products.findIndex(p => p.id === productId);

    if (productIndex === -1) {
      return res.status(404).json(createErrorResponse('Product not found', 404));
    }

    db.products[productIndex].featured = !db.products[productIndex].featured;
    db.products[productIndex].updatedAt = new Date().toISOString();

    if (saveDatabase(db)) {
      res.json(createResponse(db.products[productIndex]));
    } else {
      res.status(500).json(createErrorResponse('Failed to update product'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Category Admin Routes

// Create new category
app.post('/api/admin/categories', (req, res) => {
  try {
    const db = loadDatabase();
    const newCategory = {
      ...req.body,
      id: Math.max(...db.categories.map(c => c.id)) + 1,
      productCount: 0
    };

    db.categories.push(newCategory);

    if (saveDatabase(db)) {
      res.status(201).json(createResponse(newCategory));
    } else {
      res.status(500).json(createErrorResponse('Failed to save category'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Update category
app.put('/api/admin/categories/:id', (req, res) => {
  try {
    const db = loadDatabase();
    const categoryId = parseInt(req.params.id);
    const categoryIndex = db.categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.status(404).json(createErrorResponse('Category not found', 404));
    }

    db.categories[categoryIndex] = {
      ...db.categories[categoryIndex],
      ...req.body,
      id: categoryId
    };

    if (saveDatabase(db)) {
      res.json(createResponse(db.categories[categoryIndex]));
    } else {
      res.status(500).json(createErrorResponse('Failed to update category'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Delete category
app.delete('/api/admin/categories/:id', (req, res) => {
  try {
    const db = loadDatabase();
    const categoryId = parseInt(req.params.id);
    const categoryIndex = db.categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.status(404).json(createErrorResponse('Category not found', 404));
    }

    const deletedCategory = db.categories.splice(categoryIndex, 1)[0];

    if (saveDatabase(db)) {
      res.json(createResponse({ message: 'Category deleted successfully', category: deletedCategory }));
    } else {
      res.status(500).json(createErrorResponse('Failed to delete category'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// Toggle category featured status
app.patch('/api/admin/categories/:id/featured', (req, res) => {
  try {
    const db = loadDatabase();
    const categoryId = parseInt(req.params.id);
    const categoryIndex = db.categories.findIndex(c => c.id === categoryId);

    if (categoryIndex === -1) {
      return res.status(404).json(createErrorResponse('Category not found', 404));
    }

    db.categories[categoryIndex].featured = !db.categories[categoryIndex].featured;

    if (saveDatabase(db)) {
      res.json(createResponse(db.categories[categoryIndex]));
    } else {
      res.status(500).json(createErrorResponse('Failed to update category'));
    }
  } catch (error) {
    res.status(500).json(createErrorResponse(error.message));
  }
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json(createErrorResponse('API endpoint not found', 404));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ORANGEYA API Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📦 Products API: http://localhost:${PORT}/api/products`);
  console.log(`📂 Categories API: http://localhost:${PORT}/api/categories`);
});

export default app;
